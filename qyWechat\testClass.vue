<template>
  <page-meta :page-style="'overflow:' + (showTime ? 'hidden' : 'visible')"></page-meta>
  <view class="plr-30 pb-30">
    <u-navbar title="试课单" bgColor="#f3f8fc" placeholder>
      <view class="" slot="left">
        <u-icon name="arrow-left" size="24" bold color="#000" @click="goBack"></u-icon>
      </view>
      <view class="u-nav-slot" slot="center" style="font-weight: 600">{{ navBarTitle }}</view>
    </u-navbar>
    <view class="bg-ff plr-20 pb-40 radius-15 positionRelative">
      <block v-if="orderStatus.type == 1">
        <view class="flex-coum" :style="{ height: useHeight + 'rpx' }">
          <image :src="img1" style="width: 130rpx; height: 130rpx" mode="aspectFill"></image>
          <block v-if="!orderStatus.recvingTime && !orderStatus.teacherName && !orderStatus.teamName">
            <view class="mt-40" style="color: #666666; font-size: 30rpx">已被接单</view>
          </block>
          <block v-else>
            <view class="mt-40" style="color: #666666; font-size: 30rpx" v-if="orderStatus.teacherName">
              <text>已被</text>
              <text style="color: #000; font-weight: 700">{{ orderStatus.teacherName }}</text>
              <text>教练接单</text>
            </view>
            <view class="mt-40" style="color: #666666; font-size: 30rpx" v-if="orderStatus.teamName">
              <text>已被</text>
              <text style="color: #000; font-weight: 700">{{ orderStatus.teamName }}</text>
              <text>小组接单</text>
            </view>
            <view class="mt-40" style="color: #666666; font-size: 30rpx">接单时间：{{ orderStatus.recvingTime }}</view>
          </block>
        </view>
      </block>
      <block v-if="orderStatus.type == 2">
        <view class="flex-coum" :style="{ height: useHeight + 'rpx' }">
          <image :src="img2" style="width: 130rpx; height: 130rpx" mode="aspectFill"></image>
          <view class="mt-40" style="color: #666666; font-size: 30rpx; text-align: center">因未及时接单</view>
          <view class="mt-10" style="color: #666666; font-size: 30rpx; text-align: center">已被转移至其他交付中心</view>
        </view>
      </block>
      <block v-if="orderStatus.type == 3">
        <!-- <form id="#nform"> -->
        <view class="information ptb-15 borderB">
          <view style="width: 65%" class="f-30 bold">课程类型：</view>

          <!-- 	 <view class="flex-a-c mr-30" style="width: 50%">
                  {{infolist.curriculumName||''}}
               </view> -->
          <view class="phone-input">
            <input type="text" :value="infolist.curriculumName" name="trialname" class="input c-00" :disabled="true" />
          </view>
        </view>
        <view class="information ptb-15 borderB">
          <view style="width: 65%" class="f-30 bold">试课人姓名：</view>
          <view class="phone-input">
            <input type="text" @input="inputName" :value="infolist.expName" name="trialname" placeholder="请输入试课人姓名" class="input c-00" :disabled="isDisable" />
          </view>
        </view>
        <view class="information ptb-15 borderB">
          <view style="width: 65%" class="f-30 bold">试课人年级：</view>
          <view class="phone-input ptb-5 positionRelative">
            <view class="icon_x" v-if="!isDisable">
              <u-icon name="arrow-right" color="#c7c7c7" size="30"></u-icon>
            </view>
            <!--       <uni-data-select
                v-if="isDisable"
                :disabled="true"
                :clear="false"
                class="uni-select c-99 w100"
                v-model="infolist.grade"
                :localdata="gradelist"
                @change="choice"
              ></uni-data-select> -->
            <view class="uni-input c-00">
              {{ getGradeVal(infolist.grade) }}
            </view>
            <uni-data-select v-if="!isDisable" class="uni-select c-99 w100" v-model="infolist.grade" :localdata="gradelist" @change="choice"></uni-data-select>
          </view>
        </view>
        <view class="information ptb-15 borderB">
          <view style="width: 65%" class="f-30 bold">试课人性别：</view>
          <view class="phone-input ptb-5 positionRelative">
            <view class="icon_x" v-if="!isDisable">
              <u-icon name="arrow-right" color="#c7c7c7" size="30"></u-icon>
            </view>
            <!--     <uni-data-select
                v-if="isDisable"
                :disabled="true"
                :clear="false"
                class="uni-select c-99 w100"
                v-model="infolist.sex"
                :localdata="range"
                @change="choose"
              ></uni-data-select> -->
            <view class="uni-input c-00">
              {{ infolist.sex == 1 ? '男' : '女' }}
            </view>
            <uni-data-select v-if="!isDisable" class="uni-select c-99 w100" v-model="infolist.sex" :localdata="range" @change="choose"></uni-data-select>
          </view>
        </view>
        <view class="information ptb-30 borderB">
          <view style="width: 73%" class="f-30 bold">试课人联系方式：</view>
          <view class="phone-input" style="padding-left: 0">
            <input @input="inputNumber" :value="infolist.expPhone" name="number" placeholder="请输入试课人联系方式" class="input c-00" maxlength="11" :disabled="isDisable" />
          </view>
        </view>
        <view class="information ptb-30 borderB">
          <view style="width: 70%" class="f-30">
            <view class="bold">预约试课时间：</view>
            <view class="c-99 f-24" v-if="!isDisable">填写限制24小时之后</view>
          </view>
          <view class="uni-list-cell-db flex-s pr-20 flex-s positionRelative" v-if="isDisable">
            <view>
              <view class="">
                <!-- <text class="uni-input c-00">{{ infolist.expectTime }}</text> -->
                <text class="uni-input c-00">{{ getShowTime1(infolist.expectTime) }}</text>
              </view>
              <view class="">
                <!-- <text class="uni-input c-00">{{ infolist.expectTime }}</text> -->
                <text class="uni-input c-00">{{ getShowTime2(infolist.expectTime) }}</text>
              </view>
            </view>
          </view>
          <view class="uni-list-cell-db flex-s pr-20 flex-s positionRelative" @click="openTime" v-if="!isDisable">
            <view class="">
              <!-- <text style="float: left;"
                                  :class="date =='' ?'regions':'date_color'">{{date=='' ? '请选择' : date}}</text> -->
              <text style="float: left" class="regions" v-if="!fristDate">请选择</text>
              <view class="" v-else>
                <view>
                  <text style="float: left" class="date_color">{{ fristDate }}</text>
                </view>
                <view>
                  <text style="float: left" class="date_color">{{ lastDate }}</text>
                </view>
              </view>
            </view>
            <view class="time-icon">
              <u-icon v-if="!isDisable" name="arrow-right" color="#c7c7c7" size="30"></u-icon>
            </view>
          </view>
        </view>

        <view class="information ptb-15 borderB">
          <view style="width: 73%" class="f-30 bold">试课人所在区域：</view>
          <view class="regions-input flex-s ptb-20 pr-20 positionRelative">
            <view class="icon_x" v-if="!isDisable">
              <u-icon name="arrow-right" color="#c7c7c7" size="30"></u-icon>
            </view>
            <picker mode="region" class="f-30 lh-40 mt-5 w100 pl-8" @change="bindRegionChange" :disabled="isDisable" style="height: 100%">
              <text v-if="region" class="c-00">{{ region[0] }}{{ region[1] }}{{ region[2] }}</text>
              <text v-else :class="place == '' ? 'regions' : 'date_color'">{{ place == '' ? '请选择' : place }}</text>
            </picker>
            <uni-icons v-if="isShow" type="bottom" size="14" color="#999"></uni-icons>
          </view>
        </view>

        <view class="information ptb-15 borderB" v-if="infolist.curriculumName == '鼎英语'">
          <view style="width: 65%" class="f-30 bold">英语分数：</view>
          <view class="phone-input">
            <input type="text" @input="inputScore" :value="infolist.score" name="trialname" placeholder="请输入" class="input c-00" v-if="!isDisable" />
            <view class="" v-if="isDisable">
              {{ infolist.score || '' }}
            </view>
            <span class="ml-40 c-00">分</span>
          </view>
        </view>

        <view class="borderB pt-30 pb-25" v-if="infolist.curriculumName == '鼎英语'">
          <view class="flex-a-c f-30">
            <view class="bold" style="width: 60%" v-if="!isDisable">试课对象：</view>
            <view class="bold" style="width: 42%" v-else>试课对象：</view>
            <view v-if="!isDisable" class="w100 flex-a-c">
              <view class="flex-a-c mr-30">
                <uni-icons :type="current == 0 ? 'circle' : 'circle-filled'" :color="current == 0 ? '#999' : '#2e896f'" size="22" @click="changeCurrent(1)"></uni-icons>
                <span class="ml-15 c-66">B端</span>
                <!-- 是 -->
              </view>

              <view class="flex-a-c ml-40">
                <uni-icons :type="index == 0 ? 'circle' : 'circle-filled'" :color="index == 0 ? '#999' : '#2e896f'" size="22" @click="changeIndex(0)"></uni-icons>
                <span class="ml-15 c-66">C端</span>
                <!-- 否 -->
              </view>
            </view>
            <!-- 1 B端   2 C端 -->
            <view v-else>
              {{ infolist.experienceObject == 2 ? 'C端' : infolist.experienceObject == 1 ? 'B端' : '无' }}
            </view>
          </view>

          <view class="f-30 mt-10">
            <view v-if="!isDisable && current == 1" class="flex-a-c">
              <view class="bold" style="width: 65%">客户姓名：</view>
              <view class="phone-input">
                <input type="text" @input="inputClientName" :value="infolist.clientName" placeholder="请输入" class="input c-00" :disabled="isDisable" />
              </view>
            </view>
            <view v-if="isDisable && infolist.experienceObject == 1 && infolist.clientName && infolist.clientName != ''" class="flex-a-c mt-30 borderT pt-30">
              <view class="bold" style="width: 42%">客户姓名：</view>
              <view>{{ infolist.clientName }}</view>
            </view>
          </view>
        </view>

        <view class="flex-a-c ptb-15 borderB ptb-30 f-30">
          <view class="bold" style="width: 60%" v-if="!isDisable">咨询师：</view>
          <view class="bold" style="width: 42%" v-else>咨询师：</view>
          <view v-if="!isDisable" class="w100 flex-a-c">
            <view class="flex-a-c mr-30">
              <uni-icons
                :type="counselorOther == 0 ? 'circle' : 'circle-filled'"
                :color="counselorOther == 0 ? '#999' : '#2e896f'"
                size="22"
                @click="changeCounselorOther(1)"
              ></uni-icons>
              <span class="ml-15 c-66">上级推荐人</span>
              <!-- 是 -->
            </view>

            <view class="flex-a-c ml-40">
              <uni-icons
                :type="counselorSelf == 0 ? 'circle' : 'circle-filled'"
                :color="counselorSelf == 0 ? '#999' : '#2e896f'"
                size="22"
                @click="changeCounselorSelf(0)"
              ></uni-icons>
              <span class="ml-15 c-66">自己</span>
              <!-- 否 -->
            </view>
          </view>
          <view v-else>
            {{ infolist.counselor == '0' ? '自己' : infolist.counselor == '1' ? '上级推荐人' : infolist.counselor == '' ? '无' : infolist.counselor }}
          </view>
        </view>

        <view class="flex-a-c ptb-15 borderB ptb-30 f-30" v-if="infolist.curriculumName == '鼎英语'">
          <view class="bold" style="width: 60%" v-if="!isDisable">英语课外辅导：</view>
          <view class="bold" style="width: 42%" v-else>英语课外辅导：</view>
          <view v-if="!isDisable" class="w100 flex-a-c">
            <view class="flex-a-c mr-30">
              <uni-icons :type="radio == 0 ? 'circle' : 'circle-filled'" :color="radio == 0 ? '#999' : '#2e896f'" size="22" @click="changeRadio(1)"></uni-icons>
              <span class="ml-15 c-66">是</span>
            </view>

            <view class="flex-a-c ml-40">
              <uni-icons :type="val == 0 ? 'circle' : 'circle-filled'" :color="val == 0 ? '#999' : '#2e896f'" size="22" @click="changeVal(0)"></uni-icons>
              <span class="ml-15 c-66">否</span>
            </view>
          </view>
          <view v-else>{{ infolist.classInstruction == 0 ? '否' : '是' }}</view>
        </view>

        <view class="ptb-30" style="padding-bottom: 60rpx">
          <view class="f-30 bold">体验需求：</view>
          <view class="mt-30 p-30 bg-f7 radius-15" v-if="!isDisable">
            <textarea @input="inputRemark" v-model="remark" placeholder-style="color:#999" placeholder="请输入" :disabled="isDisable" />
          </view>

          <view class="mt-30" v-else>
            {{ infolist.remark || '' }}
          </view>
        </view>
        <!-- </form> -->
        <view class="tips" :style="{ height: svHeight + 'px' }" v-if="orderStatus.type == 3 && orderType == 0">
          <button class="phone-btn" @click="receive">接单</button>
          <button class="phone-btn phone-btn1" @click="nextOrder">下一单</button>
        </view>
      </block>
    </view>
  </view>
</template>

<script>
  import dayjs from 'dayjs';
  import longDate from '@/components/long-date/long-date.vue';
  import { Debounce } from '@/utils/debounce.js';
  export default {
    components: {
      longDate
    },
    data() {
      const currentDate = this.getDate({
        format: true
      });
      return {
        qrCode: '',
        codeShow: false,
        title: 'input',
        focus: false,
        inputValue: '',
        changeValue: '',
        mobile: '', // 推荐人手机号
        trialname: '', // 试课人姓名
        number: '', // 试课人手机号
        date: '', // 日期
        fristDate: '', // 月日周几
        lastDate: '', //上午/下午/晚上 + 具体时间
        show: false, // 是否显示日期
        region: '',
        gender: '', //性别
        range: [
          {
            value: 1,
            text: '男'
          },
          {
            value: 2,
            text: '女'
          }
        ],
        grade: '', //年级
        infolist: {}, // 试课单回显信息
        place: '', // 区域

        gradelist: [],
        orderId: '',
        deliverMerchant: '',
        payStatus: '2', //1填写试课单   2查看试课单
        list: '',
        isShow: false, //是否显示按钮
        isDisable: true, //是否禁用
        useHeight: 0, //除头部之外高度

        svHeight: 50,

        imgHost: getApp().globalData.imgsomeHost,

        datevalue: '',
        startTime: '', // 开始时间
        showTime: false,
        time: '',
        index: 0, //
        current: 0, // 是否住校（是否需要咨询）

        radio: 0,
        val: 0,
        score: '', // 英语成绩
        residentialSchool: '', // 是否住校（是否需要咨询）
        classInstruction: '', // 英语课外辅导
        remark: '', // 体验需求
        flag: false, // 防止重复点击

        disabled: false,

        clientName: '', //客户姓名

        //咨询师选择
        counselor: '',
        counselorOther: 0,
        counselorSelf: 0,

        trialTimeData: [],
        normalDateBack: {},
        choseDateBack: {},
        sureChoseData: null,
        newDate: '', //新日期
        weekdaysShort: '周日_周一_周二_周三_周四_周五_周六'.split('_'),
        gradeNameArr: '一年级_二年级_三年级_四年级_五年级_六年级_初一_初二_初三_高一_高二_高三_大一_大二_大三_大四_其他_幼儿园'.split('_'),
        timeTipStr: '',
        orderType: 0,
        orderStatus: {},
        img1: 'https://document.dxznjy.com/automation/1721727506000',
        img2: 'https://document.dxznjy.com/automation/1721727522000',
        navBarTitle: '试课单',
        app: 0,
        isProcessingBack: false,
        needBackList: 1 // 1需要返回接单列表 0需要返回接单大厅退出小程序
      };
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h - 65;
        }
      });
    },
    onLoad(e) {
      console.log(e);
      if (e.token) {
        uni.setStorageSync('token', e.token);
      }
      // #ifdef APP-PLUS
      this.app = e.app ? e.app : '';
      this.needBackList = e.needBackList ? e.needBackList : this.needBackList;
      // #endif
      this.orderId = e.id ? e.id : e.orderId ? e.orderId : '';
      // this.payStatus = e.payStatus;
      if (e.orderType) {
        this.orderType = e.orderType;
      }
      if (e.deliverMerchant) {
        this.deliverMerchant = e.deliverMerchant;
      }
      if (this.deliverMerchant && this.orderType == 0) {
        this.getStatus();
      } else {
        this.orderStatus.type = 3;
        this.init();
      }
    },

    onShow() {
      if (this.deliverMerchant && this.orderType == 0) {
        this.getStatus();
      } else {
        this.init();
      }
      if (this.payStatus == 1) {
        ///初始化时间选择
        this.normalDateBack = {
          timeWeek: this.getTimeWeek(),
          time: this.getTime2(),
          isRequest: true,
          valueArr: [0, 0]
        };
        this.seletTime(this.normalDateBack, true);
      }
    },
    onBackPress() {
      if (this.isProcessingBack) {
        return true; // 阻止默认返回行为
      }
      this.isProcessingBack = true;
      // 获取系统信息判断平台
      const systemInfo = uni.getSystemInfoSync();
      console.log('当前平台:', systemInfo.platform);

      if (systemInfo.platform === 'android') {
        if (this.needBackList == 1) {
          // 需要返回接单列表
          uni.redirectTo({
            url: '/qyWechat/takingOrder'
          });
        } else {
          // 需要返回接单大厅
          plus.runtime.quit(); // 调用退出应用
        }
      } else if (systemInfo.platform === 'ios') {
        plus.runtime.quit(); // 调用退出应用
      }
      return true; // 必须返回 true 才能阻止默认返回
    },
    onUnload() {
      if (!this.isProcessingBack) {
        // 正常卸载逻辑
        const systemInfo = uni.getSystemInfoSync();
        if (systemInfo.platform === 'android') {
          return true;
          // uni.redirectTo({
          //   url: '/qyWechat/takingOrder'
          // });
        } else if (systemInfo.platform === 'ios') {
          plus.runtime.quit(); // 调用退出应用
        }
      }
    },
    methods: {
      // 手机号脱敏('13912345678' 转换成 '139****5678') 第3位开始替换4个
      telHide(value) {
        if (!value) {
          return '';
        } else {
          let data = value.replace(/(\d{3})\d{4}(\d*)/, '$1****$2');
          return data;
        }
      },
      goBack() {
        if (this.needBackList == 1) {
          // 需要返回接单列表
          uni.redirectTo({
            url: '/qyWechat/takingOrder'
          });
        } else {
          // 需要返回接单大厅
          plus.runtime.quit(); // 调用退出应用
        }
        // // #ifdef APP-PLUS
        // plus.runtime.quit();
        // // #endif
        // if (this.orderType == 2) {
        //   uni.navigateBack();
        // } else {
        //   uni.redirectTo({
        //     url: '/qyWechat/takingOrder'
        //   });
        // }
      },
      getStatus() {
        let that = this;
        that.$http
          .get('/deliver/web/experience/orderMessageInfo', {
            id: that.orderId,
            type: 1,
            deliverMerchant: that.deliverMerchant
          })
          .then(({ data }) => {
            that.orderStatus = data.data;
            if (that.orderStatus.type != 3) {
              that.navBarTitle = ' ';
            } else {
              that.init();
            }
          });
      },
      receive: Debounce(function () {
        let that = this;
        let mobile = uni.getStorageSync('tel');
        // uni.showLoading({
        //   title: '正在接单'
        // });
        // 接单
        that.$http
          .post('/deliver/web/experience/receiving', {
            id: that.orderId,
            mobile: mobile
          })
          .then(({ data }) => {
            if (data.success) {
              // console.log(data.data);
              // let obj = data.data;
              // obj.type = 1;
              uni.showToast({
                title: '接单成功'
              });
              setTimeout(() => {
                plus.runtime.quit();
              }, 500);
              // uni.hideLoading();
              // let item = encodeURIComponent(JSON.stringify(obj));
            }
          })
          .catch((err) => {
            uni.hideLoading();
          });
      }),
      nextOrder: Debounce(function () {
        // 下一单
        this.$http
          .get('/deliver/web/experience/nextExperience', {
            id: this.orderId,
            type: 1
          })
          .then(({ data }) => {
            console.log(data);
            if (data.success) {
              if (data.data.type == 1) {
                // uni.showLoading({
                //   title: '加载中'
                // });
                this.orderId = data.data.id;
                this.init();
                // uni.hideLoading();
              } else {
                uni.navigateTo({
                  url: `/qyWechat/formalClass?orderId=${data.data.id}`
                });
              }
            } else {
              uni.showToast({
                title: data.message,
                icon: 'none'
              });
            }
          });
      }),
      ////NEW//试课时间/////Start////
      openTime() {
        if (!this.isDisable) {
          this.getTime();
          this.showTime = true;
          //打开时间定位
          if (this.sureChoseData) {
            console.log('定位已选时间坐标');
            if (this.sureChoseData.valueArr && this.sureChoseData.valueArr.length != 0) {
              console.log(this.sureChoseData.valueArr);
              this.choseDateBack = this.sureChoseData;
              this.choseDateBack.isRequest = true;
              this.seletTime(this.choseDateBack, false, true);

              this.$refs.choseDate.getDataforChoseIndex(this.sureChoseData.valueArr);
            }
          } else {
            console.log('定位当前时间坐标');
            this.choseDateBack = this.normalDateBack;
            this.choseDateBack.isRequest = true;
            this.seletTime(this.choseDateBack, false, true);

            let index = this.getCurHourIndex();
            this.$refs.choseDate.getDataforChoseIndex([0, index]);
          }
          this.$refs.dataShow.open();
        }
      },
      //修改查看试课单的时间
      getShowTime1(date) {
        if (date) {
          // let time = new Date(date);
          let time = new Date(date.replace(/-/g, '/')); //ios适配
          let dateTime = dayjs(time).format('MM月DD日');
          let week = dayjs(time).get('day');

          return `${dateTime} ${this.weekdaysShort[week]} `;
        }
        return '';
      },
      getShowTime2(date) {
        if (date) {
          let dayStage = '';
          let time = new Date(date.replace(/-/g, '/')); //ios适配
          let hour = dayjs(time).get('hour');
          let minute = dayjs(time).get('minute');
          let endHour = dayjs(time).add(1, 'hour').get('hour');
          if (hour >= 8 && hour < 12) {
            dayStage = '上午';
          } else if (hour >= 12 && hour < 17) {
            dayStage = '下午';
          } else if (hour >= 17 && hour < 24) {
            dayStage = '晚上';
          } else {
            dayStage = '';
          }
          return `${dayStage} ${this.addZero(hour)}:${this.addZero(minute)}~${this.addZero(endHour)}:${this.addZero(minute)}`;
        }
        return '';
      },
      addZero(data) {
        return data < 10 ? `0${data}` : data;
      },
      //当前时间 格式YYYY-MM-DD
      getTime2() {
        let nowDate = Date.now();
        let endTime = dayjs(nowDate).add(1, 'day').format('YYYY-MM-DD');
        return endTime;
      },
      //当前时间 格式 月 日 周
      getTimeWeek() {
        let nowDate = Date.now();
        let endTime = dayjs(nowDate).add(1, 'day').format('MM月DD日');
        let week = dayjs(nowDate).add(1, 'day').get('day');
        return endTime + ' ' + this.weekdaysShort[week];
      },
      cancelAtion() {
        this.showTime = false;
        this.$refs.dataShow.close();
      },
      confirm() {
        let dayStage = null;
        if (this.choseDateBack.hour.isNormal) {
          uni.showToast({
            icon: 'none',
            title: '时间段状态加载中~'
          });
          return;
        }
        // isSameOrAfter
        if (!dayjs(this.choseDateBack.time + ' ' + this.choseDateBack.hour.startTime).isAfter(dayjs(Date.now()).add(24, 'hour'))) {
          uni.showToast({
            icon: 'none',
            title: '请选择24小时后的时间~'
          });
          return;
        }
        if (!this.choseDateBack.hour.canReserve) {
          uni.showToast({
            icon: 'none',
            title: '该时段预约已满，请选择其他时段'
          });
          // this.timeTipStr = "该时段预约已满，请选择其他时段"
          // this.$refs.notifyPopup.open();
          // setTimeout(()=>{
          // 	this.$refs.notifyPopup.close();
          // },1500)
          return;
        }
        let time = this.timeToMinutes(this.choseDateBack.hour.startTime);

        if (time >= 480 && time < 720) {
          dayStage = '上午';
        } else if (time >= 720 && time < 1020) {
          dayStage = '下午';
        } else if (time >= 1020 && time < 1380) {
          dayStage = '晚上';
        } else {
          return dayStage;
        }

        // 确定后显示时间
        this.fristDate = this.choseDateBack.timeWeek;
        this.lastDate = `${dayStage} ${this.choseDateBack.hour.startTime}~${this.choseDateBack.hour.endTime}`;
        this.newDate = this.choseDateBack.time + ' ' + this.choseDateBack.hour.startTime;
        this.sureChoseData = this.choseDateBack;

        this.show = true;
        this.cancelAtion();
        this.$forceUpdate();
      },
      timeToMinutes(time) {
        const [hour, minute] = time.split(':');
        return parseInt(hour) * 60 + parseInt(minute);
      },

      seletTime(val, isnormal, openPopup) {
        console.log(val);
        //请求数据接口
        this.choseDateBack = val;

        if (val.isRequest) {
          //默认数据用于定位日期
          this.getNormalData(isnormal, openPopup);
          this.getServiceData(isnormal, openPopup);
        } else {
          this.choseDateBack.hour = this.trialTimeData[this.choseDateBack.valueArr[1]];
        }
      },
      //请求状态
      async getServiceData(isnormal, openPopup) {
        let res = await this.$http.get(`/deliver/app/common/selUseExperienceUsableTime?date=${this.choseDateBack.time}`);
        console.log(res);
        if (res && res.data && res.data.data) {
          this.trialTimeData = [];
          this.trialTimeData = res.data.data;
          this.timeDataOpen(isnormal, openPopup);
        }
      },
      //默认数据
      getNormalData(isnormal, openPopup) {
        console.log('--getNormalData--');
        this.trialTimeData = [];
        for (let i = 8; i < 23; i++) {
          let data1 = {};
          data1.startTime = i < 10 ? `0${i}:00` : `${i}:00`;
          data1.endTime = i + 1 < 10 ? `0${i + 1}:00` : `${i + 1}:00`;
          data1.canReserve = false;
          data1.isNormal = true;
          let data2 = {};
          data2.startTime = i < 10 ? `0${i}:30` : `${i}:30`;
          data2.endTime = i + 1 < 10 ? `0${i + 1}:30` : `${i + 1}:30`;
          data2.canReserve = false;
          data2.isNormal = true;
          this.trialTimeData.push(data1);
          this.trialTimeData.push(data2);
        }
        this.timeDataOpen(isnormal, openPopup);
      },
      timeDataOpen(isnormal, openPopup) {
        let index = this.getCurHourIndex();
        if (this.choseDateBack.time == this.getTime2()) {
          this.trialTimeData = this.trialTimeData.slice(index, this.trialTimeData.length);
        }
        this.$refs.choseDate.getDataforChoseDate(this.trialTimeData);
        if (isnormal) {
          let index = this.getCurHourIndex();
          this.choseDateBack.hour = this.trialTimeData[index];
          this.choseDateBack.valueArr[1] = index;
          this.normalDateBack.hour = this.trialTimeData[index];
          this.normalDateBack.valueArr[1] = index;
        } else {
          if (!openPopup) {
            this.choseDateBack.valueArr[1] = 0;
            this.choseDateBack.hour = this.trialTimeData[this.choseDateBack.valueArr[1]];
            this.$refs.choseDate.getDataforChoseIndex(this.choseDateBack.valueArr);
          }
        }
      },
      //定位到当前时间段
      getCurHourIndex() {
        let today = new Date();
        let currentHour = today.getHours();
        let currentMinute = today.getMinutes();

        let currentTime = [];
        if (currentMinute < 30) {
          //小于半小时 则hour 不变 min等于半小时
          currentTime = [currentHour, 30];
        } else {
          //大于等于半小时  则hour+1 min等于0
          currentTime = [currentHour + 1, 0];
        }
        for (let i = 0; i < this.trialTimeData.length; i++) {
          const [startHour, startMinute] = this.getStartTimeArr(this.trialTimeData[i].startTime);
          if (startHour == currentTime[0] && startMinute == currentTime[1]) {
            return i;
          }
        }
        return 0;
      },

      getStartTimeArr(time) {
        let timeArray = time.split(':');
        let hour = parseInt(timeArray[0]);
        let minute = parseInt(timeArray[1]);
        return [hour, minute];
      },
      ///NEW///试课时间/////End////

      formatter(type, value) {
        if (type === 'year') {
          return `${value}年`;
        }
        if (type === 'month') {
          return `${value}月`;
        }
        if (type === 'day') {
          return `${value}日`;
        }
        if (type === 'hour') {
          return `${value}时`;
        }
        if (type === 'minute') {
          return `${value}分`;
        }
        return value;
      },
      getTime() {
        let nowDate = Date.now();
        // let setDate = dayjs(date).unix() * 1000;
        let endTime = dayjs(nowDate).format('YYYY-MM-DD HH:mm');
        let endTimes = dayjs(nowDate).add(1, 'day');
        this.time = dayjs(endTimes).valueOf();
        console.log(nowDate, endTime);
      },
      changeCurrent(value) {
        this.current = 1;
        this.index = 0;
        this.residentialSchool = value;
      },
      changeIndex(e) {
        this.residentialSchool = e;
        this.current = 0;
        this.index = 1;
      },
      changeRadio(value) {
        this.radio = 1;
        this.val = 0;
        this.classInstruction = value;
      },
      changeVal(e) {
        this.radio = 0;
        this.val = 1;
        this.classInstruction = e;
      },
      //咨询师
      changeCounselorOther(value) {
        this.counselorOther = 1;
        this.counselorSelf = 0;
        this.counselor = value;
      },
      changeCounselorSelf(e) {
        this.counselorOther = 0;
        this.counselorSelf = 1;
        this.counselor = e;
      },

      cancel() {
        this.showTime = false;
      },

      handleSubmit(e) {
        console.log(e);
        // {year: "2023", month: "07", day: "11", hour: "15", minute: "21", seconds: '55'}
        // this.birthday = `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}:${seconds}`;
      },

      async init() {
        let that = this;
        await that.getGrade();
        if (that.payStatus == 2) {
          that.isShow = false;
          // that.$refs.trialLook.trialShow=true;
          that.getEchoinfo(true);
        } else {
          that.getEchoinfo(false);
        }
      },
      inputName(e) {
        this.trialname = e.detail.value;
      },
      inputNumber(e) {
        this.number = e.detail.value;
        // console.log(this.number);
      },
      inputScore(e) {
        this.score = e.detail.value;
      },
      inputClientName(e) {
        this.clientName = e.detail.value;
      },

      inputRemark(e) {
        this.remark = e.detail.value;
      },

      // 日期
      bindDateChange: function (e) {
        this.date = dayjs(e.value).format('YYYY-MM-DD HH:mm');
        console.log(this.date);
        this.show = true;
        this.showTime = false;
        this.$forceUpdate();
      },

      getDate(type) {
        const date = new Date();
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        if (type === 'start') {
          year = year - 60;
        } else if (type === 'end') {
          year = year + 2;
        }
        month = month > 9 ? month : '0' + month;
        day = day > 9 ? day : '0' + day;
        return `${year}-${month}-${day}`;
      },

      // 城市
      bindRegionChange: function (e) {
        this.region = e.detail.value;
      },
      getGradeVal(val) {
        if (val) {
          val -= 1;
          let index = 0;
          if (val <= 0) {
            index = 0;
          } else if (val >= this.gradeNameArr.length) {
            index = this.gradeNameArr.length - 1;
          } else {
            index = val;
          }
          return this.gradeNameArr[index];
        }
        return this.gradeNameArr[this.gradeNameArr.length - 1];
      },
      //性别下拉框
      choose(e) {
        this.gender = e;
      },
      //年级
      choice(e) {
        this.grade = e;
      },
      //年级
      async getGrade() {
        let res = await this.$http.get('/znyy/bvstatus/GradeType');
        if (res.data.success) {
          let list = res.data.data;
          if (this.gradelist.length == 0) {
            list.forEach((item) =>
              this.gradelist.push({
                value: Number(item.value),
                text: item.label,
                children: item.children,
                ext: item.ext
              })
            );
          }
        } else {
          this.$util.alter(res.data.message);
        }
      },
      // 新增试课单
      /*   async getTrial() {
      let _this = this;
      if (_this.flag) {
        return;
      }
      _this.flag = true;
      _this.disabled = true;
      if (_this.trialname == '') {
        _this.flag = false;
        _this.disabled = false;
        return $showError('请输入姓名');
      }
      if (_this.grade == '') {
        _this.flag = false;
        _this.disabled = false;
        return $showError('请选择年级');
      }
      if (_this.gender == '') {
        _this.flag = false;
        _this.disabled = false;
        return $showError('请选择性别');
      }
      if (!Util.isMobile(_this.number)) {
        _this.flag = false;
        _this.disabled = false;
        return $showError('请输入试课人正确的联系方式');
      }
      if (_this.newDate == null || _this.newDate == '') {
        // if (_this.date == null) {
        _this.flag = false;
        _this.disabled = false;
        return $showError('请选择期望试课时间');
      }
      if (_this.region == null) {
        return $showError('请选择试课人所在区域');
      }
      if (_this.score == '') {
        _this.flag = false;
        _this.disabled = false;
        return $showError('请输入英语成绩');
      }
      if (_this.residentialSchool === '') {
        _this.flag = false;
        _this.disabled = false;
        return $showError('请选择试课对象');
      }
      // 客戶姓名
      if (_this.residentialSchool === 1 && _this.clientName == '') {
        _this.flag = false;
        _this.disabled = false;
        return $showError('请输入客户姓名');
      }
      if (_this.counselor === '') {
        _this.flag = false;
        _this.disabled = false;
        return $showError('请选择咨询师');
      }
      if (_this.classInstruction === '') {
        _this.flag = false;
        _this.disabled = false;
        return $showError('请选择是否有英语课外辅导');
      }
      // 判断试课人是否添加过家长信息
      let res1 = await this.$http.get('/deliver/web/student/contact/info/getParentByMobile', {
        mobile: _this.number
        // referenceId: userId
      });
      if (!res1.data.data) {
        _this.flag = false;
        _this.disabled = false;
        _this.codeShow = true;
        return;
      }

      uni.showLoading();
      let data = {
        orderId: _this.orderId,
        expName: _this.trialname,
        grade: _this.grade,
        sex: _this.gender,
        expPhone: _this.number,
        expectTime: _this.newDate,
        // expectTime: _this.date,
        province: _this.region[0],
        city: _this.region[1],
        area: _this.region[2],
        score: _this.score,
        residentialSchool: '',
        classInstruction: _this.classInstruction,
        remark: _this.remark,
        clientName: _this.clientName,
        counselor: _this.counselor,
        experienceObject: _this.residentialSchool ? 1 : 2
      };
      let res = await this.$http.post('/zx/exp/save', data);
      // _this.flag= false;
      if (res) {
        uni.redirectTo({
          url: '/splitContent/officialAccount/staffCard?manageCode=123633&type=trialclass'
        });
        _this.flag = false;
        _this.disabled = false;
      } else {
        _this.flag = false;
        _this.disabled = false;
      }
      // uni.hideLoading();
    }, */

      // 试课单回显
      async getEchoinfo(isEdit) {
        let _this = this;
        uni.showLoading();
        const { data } = await this.$http.get('/deliver/web/experience/getInfo', {
          orderId: _this.orderId
        });
        uni.hideLoading();
        if (data) {
          _this.isDisable = isEdit;
          if (data.data) {
            _this.infolist = data.data;
            _this.infolist.expPhone = _this.telHide(_this.infolist.expPhone);
            _this.trialname = data.data.expName;
            _this.number = _this.telHide(data.data.expPhone);
            _this.place = _this.infolist.province + _this.infolist.city + _this.infolist.area;
          }
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  page {
    background-color: #fff;
  }

  .information {
    display: flex;
    justify-content: space-between;
    align-items: center;

    /deep/.uni-icons {
      color: #fff !important;
    }
  }

  .phone-input {
    background: #fff;
    border-radius: 8rpx;
    width: 100%;
    height: 70rpx;
    font-size: 28rpx;
    color: #999;
    display: flex;
    padding-left: 30rpx;
    align-items: center;
  }

  .name-input {
    background: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #999;
    height: 70rpx;
    display: flex;
    justify-content: space-between;
    padding: 0 30rpx;
    margin-top: 30rpx;
    align-items: center;
  }

  .uni-list-cell-db {
    background: #fff;
    border-radius: 8rpx;
    width: 100%;
    height: 70rpx;
    font-size: 28rpx;
    color: #999;
    display: flex;
    padding-left: 20rpx;
    align-items: center;
  }

  /deep/.date_color {
    color: #000 !important;
  }

  /deep/.regions {
    color: #999 !important;
    font-size: 30upx;
  }

  .regions-input {
    width: 100%;
    font-size: 30rpx;
    color: #999;
    display: flex;
    align-items: center;
  }

  .tips {
    // margin: 0 auto;
    // width: 100%;
    // background-color: red;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  /deep/.phone-btn {
    width: 310rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 40rpx;
    font-size: 30rpx;
    color: #fff !important;
    background: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  /deep/.phone-btn1 {
    border: 1rpx solid #1d755c;
    color: #1d755c !important;
    background: #fff;
  }

  /deep/.uni-select {
    padding: 0 10rpx 0 0;
    border: 0;
  }

  /deep/.uni-select__input-placeholder {
    font-size: 28rpx;
  }

  /deep/.uni-select--disabled {
    background-color: #fff;
  }

  /deep/.uni-stat__select {
    height: 60rpx !important;
  }

  .borderB {
    border-bottom: 1px solid #efefef;
  }

  /deep/.uni-select__input-placeholder {
    color: #999 !important;
    font-size: 30rpx !important;
  }

  .icon_x {
    position: absolute;
    top: 28rpx;
    right: 0;
    z-index: 1;
  }

  .choose-icon2 {
    width: 35rpx;
    height: 35rpx;
  }

  .time-icon {
    /deep/.u-icon--right {
      position: absolute;
      right: 0;
      top: 20rpx;
    }
  }

  /deep/.u-picker__view {
    height: 600rpx !important;
  }

  .dialogBG {
    margin: 0 20rpx 20rpx 20rpx;
    height: 590rpx;
    background-color: #fff;
    border-radius: 12rpx;
  }

  .top-button {
    margin-top: 20rpx;
    text-align: center;
    height: 80rpx;
    display: flex;
    justify-content: space-evenly;
  }

  .confirm-button {
    width: 210rpx;
    height: 80rpx;
    background-color: #2e896f;
    color: #fff;
    font-size: 32rpx;
    display: flex;
    justify-content: center;
    /* 文本水平居中对齐 */
    align-items: center;
    /* 文本垂直居中对齐 */
  }

  .cancel-button {
    width: 210rpx;
    height: 80rpx;
    border: 1px solid #2e896f;
    color: #2e896f;
    font-size: 32rpx;
    display: flex;
    justify-content: center;
    /* 文本水平居中对齐 */
    align-items: center;
    /* 文本垂直居中对齐 */
    overflow: visible;
  }

  .borderT {
    border-top: 1px solid #efefef;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 75rpx;
    z-index: -1;
  }

  .orderinfo {
    position: relative;
    width: 500rpx;
    font-size: 30rpx;
    border-radius: 24rpx;
    padding: 50rpx 0;
    background-color: #fff;

    .color {
      color: #7a7a7a;
    }

    .btns {
      display: flex;
      justify-content: space-around;
      margin-top: 20rpx;
    }

    .btn {
      width: 160rpx;
      height: 50rpx;
      border-radius: 50rpx;
      line-height: 50rpx;
      text-align: center;
      font-size: 20rpx;
    }

    .btn1 {
      color: #64a795;
      background-color: #ffffff;
      border: 1px solid #64a795;
    }

    .btn2 {
      background-color: #469880;
      // background-image: linear-gradient(60deg, #64b3f4 0%, #c2e59c 100%);
      color: #fff;
      border: 1px solid transparent;
    }
  }

  .flex-coum {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
</style>
