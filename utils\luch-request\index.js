import Request from './request.js';
import SCRM from './scrmRequest.js';
import Config from '../config.js';
import Util from '../util.js';
import md5 from '@/utils/md5.js';
const http = new Request();
const scrmHttp = new SCRM();
var httpArray = new Array();

http.setConfig((config) => {
  /* 设置全局配置 */
  config.baseUrl = Config.DXHost; /* 根域名不同 */
  config.header = {
    'content-type': 'application/json;charset=UTF-8',
    'x-www-endorsement': '',
    'device-id': uni.getStorageSync('device-id')
  };
  return config;
});

http.interceptor.request((config, cancel) => {
  // console.log(config)
  /* 请求之前拦截器 */
  // Util.checkNetwork();
  var _token = uni.getStorageSync('token');
  var logintokenReview = uni.getStorageSync('logintokenReview');
  config.header = {
    ...config.header
  };
  // token
  if (_token != '') {
    if (logintokenReview != '' && logintokenReview != undefined) {
      //如果趣味复习有pad的token则代表在趣味复习板块
      config.header['x-www-iap-assertion'] = logintokenReview;
    } else {
      config.header['x-www-iap-assertion'] = _token;
    }
  }
  config.header['dx-source'] = 'DELIVER##WX##MINIAPP';
  const tempDxSource = uni.getStorageSync('tempDxSource');
  if (tempDxSource) {
    config.header['temp-dx-source'] = tempDxSource;
  }

  // #ifdef APP-PLUS
  const appVersion = uni.getStorageSync('appVersion');
  if (appVersion) {
    config.header['dx-app-version'] = appVersion;
  }
  // #endif

  if (config.url.indexOf('/mps/order/withdraw/unified') > -1) {
    config.header['dx-source'] = 'ZHEN_XUAN##WX##MINIAPP';
	const tempDxSource = uni.getStorageSync('temp-dx-source');
	if (tempDxSource) {
	  config.header['temp-dx-source'] = tempDxSource;
	}
  }
  //防止重复提交
  // var raw = config.url + JSON.stringify(config.data);
  // //console.log(raw)
  // var hash = md5.hex_md5(raw);
  // config.hashData = hash;
  // if (httpArray[hash]) {
  //     cancel('重复提交');
  // } else {
  //     httpArray[hash] = raw
  // }
  return config;
});
http.interceptor.response((response) => {
  // console.log(response)
  /* 请求之后拦截器 */
  delete httpArray[response.config.hashData];
  // console.log("1111"+JSON.stringify(response))
  console.log(response);
  if (response.data.code == 20000) {
    return response;
  } else if (response.statusCode == 401 || response.data.code == 50004 || response.data.code == 40008 || (response.data.code == 50001 && response.data.message == '账户已注销！')) {
    uni.showModal({
      title: '温馨提示',
      content: response.data.code,
      showCancel: false
    });
    // console.log(response)
    if (response.data?.code == 50001 && response.data?.message == '账户已注销！') {
      // uni.showToast({ title: response.data.message, icon: 'none' });
      uni.showModal({
        title: '温馨提示',
        content: response.data.message,
        showCancel: false
      });
      // #ifdef APP-PLUS
      uni.$exitLogin();
      plus.runtime.quit();
      // #endif
    }
    var islogin = uni.getStorageSync('isLogin');
    var isQywx = uni.getStorageSync('isQy');
    uni.setStorageSync('isLogin', false);
    uni.removeStorageSync('token');
    uni.removeStorageSync('scrm_token');
    uni.removeStorageSync('logintoken');
    uni.removeStorageSync('logintokenReview');
    if (islogin == '' || islogin == false) {
      var jump = uni.getStorageSync('jump'); //以下解决多次跳转登录页的重点
      if (!jump) {
        //以下做token失效的操作
        setTimeout(() => {
          // #ifdef APP-PLUS
          uni.$exitLogin();
          plus.runtime.quit();
          // #endif
          // #ifdef MP-WEIXIN
          uni.navigateTo({
            url: '/qyWechat/login_qywx'
          });
          // #endif
        }, 100);
        uni.setStorageSync('jump', 'true');
      }
      //  if (isQywx) {
      //    uni.navigateTo({
      //      url: '/qyWechat/login_qywx'
      //    });

      //  }
      // else {
      //   // uni.reLaunch({
      //   //   url: '/pages/login/login_psd'
      //   // });
      //   var jump = uni.getStorageSync('jump') //以下解决多次跳转登录页的重点
      //   if (!jump) {
      //     //以下做token失效的操作
      //     setTimeout(() => {
      //       uni.navigateTo({
      //         url: '/login/login_psd'
      //       });
      //     }, 100)
      //     uni.setStorageSync('jump', 'true')
      //   }
      // }
      // try {
      // 	const res = wx.getSystemInfoSync()
      // 	console.log(res.environment,'=================================')
      // 	if (res.environment == 'wxwork') {
      // 		uni.reLaunch({
      // 			url: '/pages/login/login_qywx'
      // 		});
      // 	} else {
      // 		// debugger
      // 		uni.reLaunch({
      // 			url: '/pages/login/login_psd'
      // 		});
      // 	}
      // } catch (e) {
      // 	//TODO handle the exception
      // 	console.log('err111111111111111111111111111')
      // }
      uni.setStorageSync('isLogin', 'islogin');
    }
    return response;
  } else {
    uni.showModal({
      title: '温馨提示',
      content: response.data.message,
      showCancel: false
    });
    // if (response.config.url.includes("/deliver/app/teacher/batchAddPlanCourse")) {
    //   uni.showModal({
    //     title: "温馨提示",
    //     content: response.data.message,
    //     showCancel: false,
    //   });
    // } else {
    //   uni.showToast({
    //     icon: "none",
    //     title: response.data.message,
    //     duration: 2000
    //   });
    // }
    return response;
  }
});
// ===========================================
scrmHttp.setConfig((config) => {
  /* 设置全局配置 */
  config.baseUrl = Config.DXHost; /* 根域名不同 */
  config.header = {
    'content-type': 'application/json;charset=UTF-8',
    'x-www-endorsement': '',
    'device-id': uni.getStorageSync('device-id')
  };
  return config;
});

scrmHttp.interceptor.request((config, cancel) => {
  /* 请求之前拦截器 */
  // Util.checkNetwork();
  var _token = uni.getStorageSync('scrm_token');
  var logintokenReview = uni.getStorageSync('logintokenReview');
  config.header = {
    ...config.header
  };
  // token
  if (_token != '') {
    if (logintokenReview != '' && logintokenReview != undefined) {
      //如果趣味复习有pad的token则代表在趣味复习板块
      config.header['x-www-iap-assertion'] = logintokenReview;
    } else {
      config.header['x-www-iap-assertion'] = _token;
    }
  }
  config.header['dx-source'] = 'DELIVER##WX##MINIAPP';
 
  // #ifdef APP-PLUS
  const appVersion = uni.getStorageSync('appVersion');
  if (appVersion) {
    config.header['dx-app-version'] = appVersion;
  }
  const tempDxSource = uni.getStorageSync('temp-dx-source');
  if (tempDxSource) {
    config.header['temp-dx-source'] = tempDxSource;
  }
  // #endif

  if (config.url.indexOf('/mps/order/withdraw/unified') > -1) {
    config.header['dx-source'] = 'ZHEN_XUAN##WX##MINIAPP';
  }

  //防止重复提交
  // var raw = config.url + JSON.stringify(config.data);
  // //console.log(raw)
  // var hash = md5.hex_md5(raw);
  // config.hashData = hash;
  // if (httpArray[hash]) {
  //     cancel('重复提交');
  // } else {
  //     httpArray[hash] = raw
  // }
  return config;
});
scrmHttp.interceptor.response((response) => {
  /* 请求之后拦截器 */
  delete httpArray[response.config.hashData];
  // console.log("1111"+JSON.stringify(response))
  if (response.data.code == 20000) {
    return response;
  } else if (response.statusCode == 401 || response.data.code == 50004 || response.data.code == 40008) {
    // console.log(response)
    var islogin = uni.getStorageSync('isLogin');
    var isQywx = uni.getStorageSync('isQy');
    uni.setStorageSync('isLogin', false);
    uni.removeStorageSync('token');
    uni.removeStorageSync('scrm_token');
    uni.removeStorageSync('logintoken');
    uni.removeStorageSync('logintokenReview');
    if (islogin == '' || islogin == false) {
      var jump = uni.getStorageSync('jump'); //以下解决多次跳转登录页的重点
      if (!jump) {
        //以下做token失效的操作
        setTimeout(() => {
          // #ifdef APP-PLUS
          uni.$exitLogin();
          plus.runtime.quit();
          // #endif
          // #ifdef MP-WEIXIN
          uni.navigateTo({
            url: '/qyWechat/login_qywx'
          });
          // #endif
        }, 100);
        uni.setStorageSync('jump', 'true');
      }
      // if (isQywx) {
      //   uni.navigateTo({
      //     url: '/qyWechat/login_qywx'
      //   });
      // }
      // else {
      //   // uni.reLaunch({
      //   //   url: '/pages/login/login_psd'
      //   // });
      //   var jump = uni.getStorageSync('jump') //以下解决多次跳转登录页的重点
      //   if (!jump) {
      //     //以下做token失效的操作
      //     setTimeout(() => {
      //       uni.navigateTo({
      //         url: '/login/login_psd'
      //       });
      //     }, 100)
      //     uni.setStorageSync('jump', 'true')
      //   }
      // }
      uni.setStorageSync('isLogin', 'islogin');
    }
    return response;
  } else {
    uni.showModal({
      title: '温馨提示',
      content: response.data.message,
      showCancel: false
    });
    // if (response.config.url.includes("/deliver/app/teacher/batchAddPlanCourse")) {
    //   uni.showModal({
    //     title: "温馨提示",
    //     content: response.data.message,
    //     showCancel: false,
    //   });
    // } else {
    //   uni.showToast({
    //     icon: "none",
    //     title: response.data.message,
    //     duration: 2000
    //   });
    // }
    return response;
  }
});
export { http, scrmHttp };
