// 鼎校测试
// let DXHost = 'http://dxcs179.ngrok.dxznjy.com';
// const DXUploadHost = 'http://dxys157.ngrok.dxznjy.com'; //图片上传域名 修改DXHost时也要修改此处//
// 线上
let DXHost = 'https://gateway.dxznjy.com';
const DXUploadHost = 'https://gateway.dxznjy.com'; //图片上传域名 修改DXHost时也要修改此处//
// let DXHost = 'https://linetest.dxznjy.com';

let NodataTip = '暂无数据~';

let WXAppId = 'wxce2bd1113a024ff6'; //助教端

let merchantCode = '';

//发布任务
let TotalTaskAmount = 1000;
let singleTaskAmount = 200;

let GradeList = [{
    key: 0,
    value: '全部'
  },
  {
    key: 1,
    value: '一年级'
  },
  {
    key: 2,
    value: '二年级'
  },
  {
    key: 3,
    value: '三年级'
  },
  {
    key: 4,
    value: '四年级'
  },
  {
    key: 5,
    value: '五年级'
  },
  {
    key: 6,
    value: '六年级'
  },
  {
    key: 7,
    value: '七年级'
  },
  {
    key: 8,
    value: '八年级'
  },
  {
    key: 9,
    value: '九年级'
  },
  {
    key: 10,
    value: '高一'
  },
  {
    key: 11,
    value: '高二'
  },
  {
    key: 12,
    value: '高三'
  },
  {
    key: 18,
    value: '幼儿园'
  }
];
let WeekList = [{
    key: 1,
    value: '星期一'
  },
  {
    key: 2,
    value: '星期二'
  },
  {
    key: 3,
    value: '星期三'
  },
  {
    key: 4,
    value: '星期四'
  },
  {
    key: 5,
    value: '星期五'
  },
  {
    key: 6,
    value: '星期六'
  },
  {
    key: 7,
    value: '星期日'
  }
];
let SexList = [{
    key: 0,
    value: '男'
  },
  {
    key: 1,
    value: '女'
  }
];

let NoNormalReasonList = [{
    key: 1,
    value: '请假'
  },
  {
    key: 2,
    value: '遗漏'
  },
  {
    key: 3,
    value: '台灯异常'
  },
  {
    key: 4,
    value: '其他'
  }
];

let evaluateList = [{
    label: '非常棒',
    value: 1,
    icon: '/ebalue_icon1.png',
    iconActive: '/ebalue_icon1_active.png'
  },
  {
    label: '还可以',
    value: 2,
    icon: '/ebalue_icon2.png',
    iconActive: '/ebalue_icon2_active.png'
  },
  {
    label: '比较差',
    value: 3,
    icon: '/ebalue_icon3.png',
    iconActive: '/ebalue_icon3_active.png'
  }
];

//加分选项
let scoreList = [{
    label: '1',
    value: 1
  },
  {
    label: '2',
    value: 2
  },
  {
    label: '3',
    value: 3
  },
  {
    label: '4',
    value: 4
  },
  {
    label: '5',
    value: 5
  }
];

//权重 优先级
let weightList = [{
    label: '无',
    value: 0,
    color: '#000000'
  },
  {
    label: '低',
    value: 1,
    color: '#2DC032'
  },
  {
    label: '中',
    value: 2,
    color: '#EB7E2D'
  },
  {
    label: '高',
    value: 3,
    color: '#CE1F1F'
  }
];

// 奖品发放状态
let prizeStatusList = [{
    label: '待家长确认',
    value: 1,
    color: '#EB7E2D'
  },
  {
    label: '家长已确认',
    value: 2,
    color: '#099AEF'
  },
  {
    label: '学员进行中',
    value: 3,
    color: '#2DC032'
  },
  {
    label: '学员已达成',
    value: 4,
    color: '#511578'
  },
  {
    label: '学员未达成',
    value: 5,
    color: '#999999'
  },
  {
    label: '奖励已发放',
    value: 6,
    color: '#28C1B3'
  },
  {
    label: '家长已驳回',
    value: 7,
    color: '#48140e'
  }
];

let ImgsomeHost = 'https://document.dxznjy.com/';
let ImguseHost = 'https://document.dxznjy.com/applet/zhujiao';

// 思维导图链接

// let allH5Url = "https://document.dxznjy.com/mallH5/h5/#/pages/";
// let allH5Url = "http://*************:8080/#/pages/";//cxy
// let allH5Url = "https://document.dxznjy.com/mallH5/h5/#/pages/";
let allH5Url = 'https://document.dxznjy.com/mallH5Link/h5/#/pages/';

// 评分反馈H5链接
let feedbackUrl = allH5Url + 'index/index';

// 学习分享会详情页H5链接
let shareDetail = allH5Url + 'learnShare/learningDetail';

let XKTCurriculumCodeArr = ['XKT', 'XKT_CZHX1', 'XKT_CZSX1', 'XKT_CZWL1', 'XKT_CZYW1', 'XKT_CZYY1', 'XKT_GZDL',
  'XKT_GZHX', 'XKT_GZLS', 'XKT_GZSW', 'XKT_GZSX', 'XKT_GZWL', 'XKT_GZYW', 'XKT_GZYY', 'XKT_GZZZ'
]; //学考通相关课程大类
let XSMCurriculumCodeArr = ['XSM_CZDL1', 'XSM_CZLS1', 'XSM_CZSW1', 'XSM_CZZZ1', 'XSM_SDHK', 'XSM_ZSZK', 'XSM_JXDK10',
  'XSM_JXSHK10', 'XSM_JXSK10'
]; //小四门相关课程大类
let MATHCurriculumCodeArr = ['MATH']; //数学相关课程大类
let CurriculumCodeArr = [...XKTCurriculumCodeArr, ...XSMCurriculumCodeArr, ...MATHCurriculumCodeArr]; //学考通、小四门所有课程大类

module.exports = {
  DXHost: DXHost,
  DXUploadHost: DXUploadHost,
  merchantCode: merchantCode,
  NodataTip: NodataTip,
  WXAppId: WXAppId,
  merchantCode: merchantCode,
  TotalTaskAmount: TotalTaskAmount,
  singleTaskAmount: singleTaskAmount,
  WeekList: WeekList,
  SexList: SexList,
  NoNormalReasonList: NoNormalReasonList,
  evaluateList: evaluateList,
  scoreList: scoreList,
  weightList: weightList,
  prizeStatusList: prizeStatusList,
  ImgsomeHost: ImgsomeHost,
  ImguseHost: ImguseHost,
  feedbackUrl: feedbackUrl,
  shareDetail: shareDetail,
  allH5Url: allH5Url,
  XKTCurriculumCodeArr,
  XSMCurriculumCodeArr,
  CurriculumCodeArr,
  MATHCurriculumCodeArr
};