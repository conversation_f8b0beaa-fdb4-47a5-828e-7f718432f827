<template>
  <view class="positionRelative w100">
    <view class="hear_img">
      <image src="http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1743558378000" class="wallet-bgc"></image>
      <view class="amount f-30">
        <view class="positionAbsolute">
          账期余额
          <uni-icons type="help" size="16" color="#fefffe" class="balance" @click="openExplain"></uni-icons>
          <view class="f-28 c-33 balance_explain" v-if="explainShow">
            <view>账期余额=可提现+已结算</view>
            <view class="mt-10 lh-50">注：每上完1学时课程，则结算20佣金到账期余额上，当月的绩效金额：为次月15日前00：00统一结算并发放到账期余额。</view>
          </view>
        </view>
        <view class="flex-s mt-30 pt-30">
          <view class="money">
            {{ balance || 0.0 }}
          </view>
          <view class="withdrawal" @click="goWallet()">提现</view>
        </view>
        <view class="flex-a-c mt-30 border-t pt-50">
          <view class="c-ea positionAbsolute">
            总收益
            <uni-icons type="help" size="16" color="#fefffe" class="total_money" @click="openIncome"></uni-icons>
            <text class="c-ff ml-40">{{ withdrawalDetails.totalMoney || 0.0 }}</text>
            <view class="total_explain c-33 f-28" v-if="incomePrompt">总收益=账期余额+累计提现</view>
          </view>
          <view class="c-ea positionAbsolute" style="padding-left: 350rpx">
            累计提现
            <text class="c-ff ml-40">{{ totalWithdrawal || 0.0 }}</text>
            <uni-icons type="help" size="16" color="#fefffe" class="taking" @click="openWithdrawal"></uni-icons>
            <view class="withdrawal_explain c-33 f-28" v-if="withdrawalPrompt">累计提现的金额</view>
          </view>
        </view>
      </view>
    </view>

    <view class="nav-title">
      <view class="status_bar">
        我的钱包
        <uni-icons type="left" size="18" class="title-icon" @click="goback"></uni-icons>
      </view>
    </view>
    <!-- <view class="tab-bg">
      <u-tabs
        :list="list"
        @click="checkIndex"
        lineWidth="40rpx"
        lineHeight="3"
        lineColor="green"
        :inactiveStyle="{ color: '#666666', fontSize: '30rpx' }"
        :activeStyle="{ color: 'green', fontWeight: 'bold', fontSize: '32rpx' }"
      ></u-tabs>
    </view> -->
    <view class="bill">
      <view class="flex-x-b flex-a-c">
        <view class="flex-a-c">
          <picker @change="bindPickerChange" :value="index" :range="columns" range-key="label">
            <view class="mr-15 f-32 bold">{{ columns[index].label }}</view>
          </picker>
          <!-- <view class="mr-15 f-32 bold" @click="show=true">{{bill?bill :'全部账单'}}</view> -->
          <u-icon name="arrow-down-fill" color="#000" size="16"></u-icon>
          <!-- <u-picker :show="show" :columns="columns" keyName="label" confirmColor="#2E896F" :immediateChange="true"
						@cancel="cancel" @confirm="confirm"></u-picker> -->
        </view>
        <!-- 	lineWidth="40rpx"
					lineHeight="4"
					lineColor="green"  fontWeight: 'bold',-->
        <view style="width: 420rpx; margin-right: -43rpx">
          <u-tabs
            :list="tabsList"
            @click="tabClick"
            lineWidth="40rpx"
            lineHeight="3"
            lineColor="green"
            :activeStyle="{
              color: 'green',
              fontWeight: 'bold',
              transform: 'scale(1.05)'
            }"
            :inactiveStyle="{
              color: '#606266',
              transform: 'scale(1)'
            }"
            itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;font-size:14px;"
          ></u-tabs>
        </view>
      </view>

      <view class="flex-s f-30 mt-30">
        <view class="flex-a-c">
          <text class="mr-10" @click="openTime">{{ date }}</text>
          <uni-icons type="bottom" size="16" color="#000"></uni-icons>
          <u-datetime-picker
            ref="datetimePicker"
            :show="showTime"
            v-model="value"
            mode="year-month"
            confirmColor="#2E896F"
            @cancel="cancelTime"
            @confirm="confirmTime"
          ></u-datetime-picker>
        </view>
        <view class="c-66">
          <text>提现￥{{ priceDetails.outAmount || 0.0 }}</text>
          <text class="ml-20">
            <text>收入￥</text>
            <text style="color: green; font-weight: bold">{{ priceDetails.inAmount || 0.0 }}</text>
          </text>
        </view>
      </view>

      <view v-if="showPages" class="scrollView" :style="{ height: useHeight + 'rpx' }" scroll-y="true" :scroll-top="scrollTop">
        <view class="f-30 bg-ff radius-15 plr-30 mt-30" v-for="(item, index) in infoLists.data" :key="index">
          <view class="f-30 bg-ff radius-15">
            <view class="ptb-30" @click="goBillDetails(item)">
              <view class="flex-s">
                <view class="c-33">{{ item.content }}</view>
                <view class="f-32 bold" :class="item.orderId == '' ? 'c-00' : 'c-fea'">
                  {{ item.orderId == '' ? '-' : '+' }}
                  <text class="bold">{{ item.amount }}</text>
                </view>
              </view>
              <!-- <view class="c-99 mt-15">{{item.createTime}}</view> -->
              <view class="c-99 mt-15">
                <text v-if="item.orderId && item.type != 7">
                  {{ item.type == 6 ? '复习时间' : '上课时间' }} ：
                  <text v-if="item.startStudyTime">
                    <text v-if="item.type != 9">{{ item.startStudyTime }}</text>
                    <text v-if="item.type != 6 && item.type != 9">-</text>
                    <text v-if="item.type == 9">{{ item.startStudyTime.slice(5, 11) }} ~ {{ item.endStudyTime.slice(5, 11) }}</text>
                    <text v-if="item.endStudyTime && item.type != 6 && item.type != 9">
                      {{ item.endStudyTime.slice(11, 17) }}
                    </text>
                  </text>
                  <text v-else>
                    <text>-</text>
                  </text>
                </text>
              </view>
              <view class="c-99 mt-15 flex-a-c flex-x-s">
                <view class="order-style-width">
                  <view>
                    <text v-if="item.orderId == ''">提现时间：</text>
                    <text v-else>到账时间：</text>
                    {{ item.orderId == '' ? item.createTime : item.accountTime.slice(0, 11) }}
                  </view>
                </view>

                <!-- v-if="item.orderId" -->
                <view style="margin-left: 30rpx" :class="item.accountStatus == 1 ? 'c-67C23A' : 'c-E6A23C'">
                  <text class="c-99">状态：</text>
                  <!-- {{ item.accountStatus == 1 ? '已到账' : item.accountStatus == 2 ? '提现中' : '未到账' }} -->
                  {{ item.accountStatusDesc }}
                </view>
              </view>
              <view class="c-99 mt-15" v-if="item.orderId != ''">
                <text>收益类型：</text>
                {{ item.wagesTypeText }}
              </view>
            </view>
          </view>
        </view>
      </view>
      <button v-if="showPages" class="export-btn-style" @click="exportBill()">导出账单</button>

      <view v-if="!showPages" class="bg-ff radius-15 mt-30 no-data" :style="{ height: useHeight + 'rpx' }">
        <image src="https://document.dxznjy.com/alading/correcting/no_data.png" class="mb-20 img_s"></image>
        <view style="color: #bdbdbd">暂无数据</view>
      </view>
    </view>
    <uni-popup ref="copyExcolPopup" type="center" :maskClick="true">
      <view class="copy-excol-popup">
        <view>点击复制按钮至浏览器可下载Excel表格，也可在线预览</view>
        <view>
          <button class="btn-style left-btn-style" @click="excelCopy()" style="margin-right: 40rpx">复制链接</button>
          <button class="btn-style" @click="excelPreview()">在线预览</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import dayjs from 'dayjs';
  import Config from '@/utils/config.js';

  const { http } = require('@/utils/luch-request/index.js');
  import Util from '@/utils/util.js';

  export default {
    data() {
      return {
        useHeight: 0,
        show: false,
        index: 0,
        status: 2,
        list: [
          {
            name: '我的收益',
            key: 2
          },
          {
            name: '历史收益',
            key: 1
          }
        ],
        tabsList: [
          {
            name: '全部',
            key: 2
          },
          {
            name: '已到账',
            key: 1
          },
          {
            name: '未到账',
            key: 0
          }
        ],
        columns: [
          {
            label: '全部账单',
            // 其他属性值
            id: 0
            // ...
          },
          {
            label: '收入明细',
            id: 1
          },
          {
            label: '提现明细',
            id: 2
          }
        ],
        range: '',
        showTime: false,
        value: Number(new Date()),
        datetime: '',
        date: '',
        downFileUrl: '', //导出文件地址
        explainShow: false, // 账期余额提示
        incomePrompt: false, // 总收益
        withdrawalPrompt: false, // 提现

        no_more: false,
        infoLists: {},
        month: '',
        page: 1,
        userCode: '',
        type: '',
        balance: '', // 账期余额
        withdrawalDetails: {}, // 提现明细
        // bill: "",
        totalWithdrawal: '', // 累计提现

        priceDetails: {}, // 收支明细
        agreementUrl: '',
        teacherName: '',
        signTittle: '',
        scrollTop: 0,
        tabKey: 2,
        showPages: false
      };
    },

    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 875;
        }
      });
      // 微信小程序需要用此写法
      this.$refs.datetimePicker.setFormatter(this.formatter);
    },
    onShow() {
      this.clickHandle();
      let withdrawal = uni.getStorageSync('withdrawal');
      if (withdrawal) {
        this.getAccount();
        this.getTeacherAccount();
        this.getAccountFlow();
        uni.removeStorage({
          key: 'withdrawal',
          success: (res) => {
            console.log('withdrawal');
          }
        });
      }
    },
    onLoad(options) {
      //#ifdef APP-PLUS

      // #endif

      this.userCode = options.userCode;
      this.teacherName = options.teacherName;
      this.month = dayjs().format('YYYY-MM');
      this.date = this.month.slice(0, 4) + '年' + this.month.slice(5, 7) + '月';
      this.getAccount();
      this.getTeacherAccount();
      this.getAccountFlow();
    },
    onPullDownRefresh() {
      this.getAccount();
      this.getTeacherAccount();
      this.getAccountFlow();
      setTimeout(() => uni.stopPullDownRefresh(), 1000);
    },
    onReachBottom() {
      if (this.page >= this.infoLists.totalPage) {
        this.no_more = true;
        return false;
      }
      this.page++;
      this.getTeacherAccount(true, this.page);
    },

    methods: {
      checkIndex(e) {
        this.explainShow = false; // 关闭弹窗
        this.tabKey = e.key;
        this.balance = 0;
        this.infoLists = {};
        this.priceDetails.outAmount = 0;
        this.priceDetails.inAmount = 0;
        this.totalWithdrawal = 0;
        this.getAccount();
        this.getTeacherAccount();
        this.getAccountFlow();
      },

      clickHandle() {
        this.scrollTop = this.scrollTop === 0 ? 1 : 0;
      },
      // scrolltolower() {
      //   if (this.page >= this.infoLists.totalPage) {
      //     this.no_more = true;
      //     return false;
      //   }
      //   this.page++;
      //   this.getTeacherAccount(true, this.page);
      // },
      //账单状态切换
      tabClick(e) {
        console.log(e);
        this.status = e.key;
        this.infoLists = {};
        this.getTeacherAccount();
        console.log('---------------------------------');
      },
      goback() {
        uni.navigateBack();
      },
      cancel() {
        this.show = false;
      },
      confirm(e) {
        console.log(e);
        this.type = e.value[0].id;
        this.bill = e.value[0].label;
        this.show = false;
        this.getTeacherAccount();
      },
      openTime() {
        this.showTime = true;
      },

      formatter(type, value) {
        if (type === 'year') {
          return `${value}年`;
        }
        if (type === 'month') {
          return `${value}月`;
        }

        return value;
      },

      bindPickerChange(e) {
        console.log(e);
        this.index = e.detail.value;
        this.type = e.detail.value;
        this.getTeacherAccount();
      },

      cancelTime() {
        this.showTime = false;
      },

      confirmTime(e) {
        console.log(e);
        this.month = dayjs(e.value).format('YYYY-MM');
        this.showTime = false;
        if (this.month != '') {
          this.date = this.month.slice(0, 4) + '年' + this.month.slice(5, 7) + '月';
        }
        this.getTeacherAccount();
        this.getAccountFlow();
      },

      openExplain() {
        this.explainShow = !this.explainShow;
      },

      openIncome() {
        this.incomePrompt = true;
        setTimeout(() => {
          this.incomePrompt = false;
        }, 1500);
      },

      openWithdrawal() {
        this.withdrawalPrompt = true;
        setTimeout(() => {
          this.withdrawalPrompt = false;
        }, 1500);
      },

      // 收入流水列表
      async getTeacherAccount(isPage, page) {
        console.log('ppppppppppppppppppppppppp');
        this.page = page || 1;
        let _this = this;
        let data = {
          month: this.month,
          pageNum: page || 1,
          pageSize: 100,
          userCode: this.userCode,
          type: this.type == 0 ? '' : this.type,
          accountStatus: this.status == 2 ? '' : this.status
        };
        let url = '/deliver/app/teacher/getTeacherAccountFlowList';
        // let url = this.tabKey == 1 ? '/deliver/app/teacher/getTeacherAccountFlowList' : '/deliver/app/teacher/getFlexPayTeacherAccountFlowList';
        let tabActive = this.tabKey;
        let res = await http.get(url, data);

        if (res) {
          if (tabActive !== this.tabKey) return;
          if (isPage) {
            let old = _this.infoLists.data;
            _this.infoLists.data = [...old, ...res.data.data.data];
          } else {
            _this.infoLists = res.data.data;
          }

          console.log(_this.infoLists.data.length, 'ppppppppppppp');
          if (_this.infoLists.data.length > 0) {
            this.showPages = true;
          } else {
            this.showPages = false;
          }
        }
      },

      // 获取俱乐部金额
      async getAccount() {
        let that = this;
        that.withdrawalDetails = {};
        // let url = that.tabKey == 1 ? '/mps/account/list/login/code/byCode' : '/flexpay/userAccount/getUserAccountInfo';
        let url = '/mps/account/list/login/code/byCode';
        let res = await http.get(url, {
          userCode: that.userCode
        });

        if (res.data.success) {
          console.log(res.data.data, 'that.withdrawalDetailsthat.withdrawalDetails');
          for (let i = 0; i < res.data.data.length; i++) {
            if (res.data.data[i].userCode == this.userCode) {
              that.withdrawalDetails = res.data.data[i] || {};
            }
          }
          // this.withdrawalDetails = res.data.data[0];

          this.balance = Util.Fen2Yuan(this.withdrawalDetails.waitAllocateFunds * 1 + this.withdrawalDetails.availableCashAmount * 1);
          this.withdrawalDetails.totalMoney = Util.Fen2Yuan(this.withdrawalDetails.totalMoney || 0); // 总收益
          this.withdrawalDetails.availableCashAmount = Util.Fen2Yuan(this.withdrawalDetails.availableCashAmount || 0); // 可提现
          console.log(this.withdrawalDetails.waitAllocateFunds * 1 + this.withdrawalDetails.availableCashAmount * 1, '=============');
          this.totalWithdrawal = (this.withdrawalDetails.totalMoney - this.balance).toFixed(2);
          console.log(this.withdrawalDetails, '==========================');
        }
      },
      /**
       * 检测是否为App环境（优先检测）
       * @returns {boolean}
       */
      _isApp() {
        // 方法1：检查 plus 对象（5+ App特有）
        if (typeof plus !== 'undefined' && plus.runtime) {
          return true;
        }

        // 方法2：检查是否在App的webview中
        if (typeof window !== 'undefined' && window.plus) {
          return true;
        }

        // 方法3：检查用户代理字符串中的App标识
        if (typeof navigator !== 'undefined' && navigator.userAgent) {
          const ua = navigator.userAgent.toLowerCase();
          // 检查常见的App标识
          if (ua.includes('html5plus') || ua.includes('streamapp') || ua.includes('hbuilder')) {
            return true;
          }
        }

        return false;
      },

      /**
       * 处理App环境下的文件路径
       * @param filePath
       * @returns {*|string}
       */
      processAppFilePath(filePath) {
        if (!filePath) return filePath;

        console.log('原始文件路径:', filePath);

        // 如果已经是正确的格式，直接返回
        if (filePath.indexOf('file://') === 0) {
          return filePath;
        }

        // 如果是相对路径，转换为绝对路径
        if (filePath.indexOf('_doc') === 0 || filePath.indexOf('_www') === 0) {
          const absolutePath = plus.io.convertLocalFileSystemURL(filePath);
          console.log('转换后的绝对路径:', absolutePath);
          return absolutePath;
        }

        // 如果是绝对路径但没有file://前缀
        if (filePath.indexOf('/') === 0) {
          return 'file://' + filePath;
        }

        return filePath;
      },

      /**
       * 检测是否为微信小程序环境
       * 通过检测wx对象、小程序特有API和全局配置来判断
       * @returns {boolean} 如果是微信小程序环境返回true，否则返回false
       * @private
       */
      _isWeChatMiniProgram() {
        // 如果已经确定是App环境，则不是小程序
        if (this._isApp()) {
          return false;
        }

        // 方法1：检查 wx 对象和小程序特有的API
        if (typeof wx !== 'undefined') {
          // 检查小程序特有的API
          const miniProgramApis = ['getSystemInfo', 'getAccountInfoSync', 'getLaunchOptionsSync'];

          const hasMiniProgramApis = miniProgramApis.some((api) => typeof wx[api] === 'function');

          if (hasMiniProgramApis) {
            try {
              // 尝试调用小程序特有的同步API
              const accountInfo = wx.getAccountInfoSync && wx.getAccountInfoSync();
              if (accountInfo && accountInfo.miniProgram) {
                return true;
              }
            } catch (e) {
              // 如果调用失败，可能不是小程序环境
            }
          }
        }

        // 方法2：检查全局对象
        if (typeof __wxConfig !== 'undefined' || typeof __wxRoute !== 'undefined') {
          return true;
        }

        // 方法3：检查用户代理字符串
        if (typeof navigator !== 'undefined' && navigator.userAgent) {
          const ua = navigator.userAgent.toLowerCase();
          if (ua.includes('miniprogram') && !ua.includes('html5plus')) {
            return true;
          }
        }

        return false;
      },

      /**
       * 导出账单 - 下载文件并重命名为Excel格式后上传
       */
      async exportBill() {
        // 判断是否微信小程序环境

        // this.userCode = '**********';

        if (this._isWeChatMiniProgram()) {
          console.log('是否是微信小程序：', '是');
          let res = await http.get(
            `/deliver/app/teacher/exportTeacherAccountFlow?userCode=${this.userCode}&type=${this.type == 0 ? '' : this.type}&month=${this.month}&status=${this.status}`,
            { responseType: 'arraybuffer' }
          );
          if (res) {
            this.loadByRes(res.data, 'xlsx');
          }
          return;
        }
        console.log('是否是微信小程序：', '不是');

        let that = this;
        console.log('🚀🥶💩~ this.tabKey', this.tabKey);
        try {
          const url = '/deliver/app/teacher/exportTeacherAccountFlow';
          const requestUrl = `${Config.DXUploadHost}${url}?userCode=${that.userCode}&type=${this.type == 0 ? '' : this.type}&month=${this.month}&status=${this.status}`;

          // 显示加载提示
          uni.showLoading({ title: '正在导出...' });

          // 发起下载请求
          uni.downloadFile({
            url: requestUrl,
            header: {
              'x-www-iap-assertion': uni.getStorageSync('token'),
              'content-type': 'application/vnd.ms-excel;charset=utf-8'
            },
            success: (res) => {
              if (res.statusCode === 200) {
                console.log('🚀 ~ exportBill ~ 下载成功，临时文件路径:', res.tempFilePath);
                // 保存到本地
                uni.saveFile({
                  tempFilePath: res.tempFilePath,
                  success: (saveRes) => {
                    console.log('🚀 ~ exportBill ~ 文件已保存:', saveRes.savedFilePath);
                    this.renameAndUploadFile(saveRes.savedFilePath);
                  },
                  fail: (err) => {
                    uni.hideLoading();
                    console.error('❌ 文件保存失败:', err);
                    uni.showToast({ title: '文件保存失败', icon: 'none' });
                  }
                });
              } else {
                uni.hideLoading();
                uni.showToast({ title: `下载失败: ${res.statusCode}`, icon: 'none' });
              }
            },
            fail: (err) => {
              uni.hideLoading();
              uni.showToast({ title: '请求失败', icon: 'none' });
              console.error('❌ 下载失败:', err);
            }
          });
        } catch (error) {
          uni.hideLoading();
          console.error('❌ 导出账单失败：', error);
          uni.showToast({
            icon: 'none',
            title: '网络错误，请重试'
          });
        }
      },

      /**
       * 重命名文件并上传到服务器
       * @param {string} originalFilePath - 原始文件路径
       */
      renameAndUploadFile(originalFilePath) {
        // 检查是否在App环境中
        if (typeof plus !== 'undefined' && plus.io) {
          // 获取文件系统根目录
          plus.io.resolveLocalFileSystemURL(
            '_doc/',
            (entry) => {
              // 生成新的文件名
              const newFileName = '账单' + Math.random().toString(36).substring(2, 15) + '.xlsx';

              // 读取原始文件
              plus.io.resolveLocalFileSystemURL(
                originalFilePath,
                (fileEntry) => {
                  // 复制文件到新位置并重命名
                  fileEntry.copyTo(
                    entry,
                    newFileName,
                    (newFileEntry) => {
                      console.log('✅ 文件重命名成功:', newFileEntry.fullPath);

                      // 上传重命名后的文件
                      this.uploadRenamedFile(newFileEntry.fullPath);
                    },
                    (error) => {
                      console.error('❌ 文件重命名失败:', error);
                      // 如果重命名失败，使用原文件上传
                      this.uploadRenamedFile(originalFilePath);
                    }
                  );
                },
                (error) => {
                  console.error('❌ 无法访问原始文件:', error);
                  uni.hideLoading();
                  uni.showToast({ title: '文件处理失败', icon: 'none' });
                }
              );
            },
            (error) => {
              console.error('❌ 无法访问文档目录:', error);
              // 如果无法访问目录，使用原文件上传
              this.uploadRenamedFile(originalFilePath);
            }
          );
        } else {
          // 非App环境，直接上传原文件
          console.log('⚠️ 非App环境，无法重命名文件，使用原文件上传');
          this.uploadRenamedFile(originalFilePath);
        }
      },

      /**
       * 上传重命名后的文件到服务器
       * @param {string} filePath - 要上传的文件路径
       */
      uploadRenamedFile(filePath) {
        const processedFilePath = this.processAppFilePath(filePath);

        console.log('🚀 开始上传文件:', processedFilePath);

        uni.uploadFile({
          url: `${Config.DXUploadHost}/zx/common/uploadFile`,
          filePath: processedFilePath,
          name: 'file',
          header: {
            'x-www-iap-assertion': uni.getStorageSync('token')
          },
          formData: {
            type: 'xlsx',
            fileName: '六月份工作.xlsx' // 指定上传时的文件名
          },
          success: (res) => {
            uni.hideLoading();
            console.log('✅ 文件上传响应:', res);
            console.log('📊 响应状态码:', res.statusCode);

            try {
              const data = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
              console.log('📋 解析后的响应数据:', data);

              const url = data?.data?.fileUrl;
              console.log('🔗 提取的文件URL:', url);

              if (url) {
                console.log('✅ 文件上传成功，URL:', url);

                // 可以在这里处理上传成功后的逻辑
                this.downFileUrl = url;
                // 如果有弹窗组件，可以打开
                this.$refs.copyExcolPopup && this.$refs.copyExcolPopup.open();
              } else {
                const errorMsg = data?.message || '上传失败';
                console.error('❌ 上传失败，错误信息:', errorMsg);
                uni.showToast({ title: errorMsg, icon: 'none' });
              }
            } catch (e) {
              console.error('❌ 响应解析失败:', e);
              uni.showToast({ title: '响应解析失败', icon: 'none' });
            }
          },
          fail: (err) => {
            uni.hideLoading();
            console.error('❌ 文件上传失败:', err);
            uni.showToast({ title: '文件上传失败', icon: 'none' });
          }
        });
      },
      loadByRes(res, type) {
        let arr = ['doc', 'xls', 'ppt', 'pdf', 'docx', 'xlsx', 'pptx'];
        if (!arr.includes(type)) {
          return uni.showToast({
            icon: 'none',
            title: '文件类型不支持'
          });
        }
        const arrayBuffer = res;
        // 将 ArrayBuffer 转换为 Blob 对象（微信小程序没有直接支持 Blob，但可以通过 base64 转换）
        const blob = uni.arrayBufferToBase64(arrayBuffer);
        const now = '账单列表' + new Date().getTime();
        // 创建一个临时文件路径（需要确保路径有效） 加入时间戳保证文件独立性
        const filePath = `${wx.env.USER_DATA_PATH}/${now}`; // 替换为你的文件名和扩展名
        console.log(wx.env.USER_DATA_PATH);
        // 将 base64 字符串转换为 ArrayBuffer，然后写入文件
        const uint8Array = uni.base64ToArrayBuffer(blob);
        // 使用 wx.getFileSystemManager().writeFile 写入文件
        const fs = wx.getFileSystemManager();
        console.log(filePath);
        fs.writeFile({
          filePath: filePath + '.' + type,
          data: uint8Array,
          encoding: 'binary',
          success: (info) => {
            setTimeout(() => {
              uni.uploadFile({
                url: `${Config.DXHost}/zx/common/uploadFile`,
                filePath: filePath + '.' + type,
                name: 'file',
                formData: {
                  user: 'test'
                },
                header: {
                  'x-www-iap-assertion': uni.getStorageSync('token')
                },
                success: (res) => {
                  let data = JSON.parse(res.data);
                  console.log(data.data.fileUrl);
                  this.downFileUrl = data.data.fileUrl;
                  this.$refs.copyExcolPopup.open();
                }
              });
            }, 500);
          },
          fail: (err) => {
            console.error('文件保存失败', err);
          }
        });
      },
      excelCopy() {
        let that = this;
        uni.setClipboardData({
          data: that.downFileUrl,
          success: function (res) {
            uni.getClipboardData({
              success: function (res) {
                uni.showToast({
                  title: '已复制到剪贴板'
                });
                that.$refs.copyExcolPopup.close();
              }
            });
          }
        });
      },
      //预览
      excelPreview() {
        let that = this;
        uni.downloadFile({
          //下载文件资源到本地,返回文件的本地临时路径
          url: that.downFileUrl, //网络图片路径
          success: (res) => {
            console.log(res, 'downloadFile');
            uni.openDocument({
              filePath: res.tempFilePath,
              fileType: 'xlsx', // 可选，指定文件类型，有助于系统更好地处理文件
              showMenu: true, //可选 是否开启保存转发等功能
              success: (openInfo) => {
                console.log(openInfo);
                console.log('文件打开成功');
                that.$refs.copyExcolPopup.close();
              },
              fail: (err) => {
                console.error('文件打开失败', err);
              }
            });
          }
        });
      },
      // 获取累计提现金额
      async getWithdrawal() {
        let that = this;
        let url = '/deliver/app/teacher/getTeacherWithdrawMoney';
        let res = await http.get(url, {
          userCode: that.userCode
        });
        if (res.data.success) {
          that.totalWithdrawal = Number(res.data.data).toFixed(2); // 累计提现
          that.balance = (Number(that.withdrawalDetails.totalMoney) - Number(that.totalWithdrawal)).toFixed(2); // 账期余额
        }
      },

      //获取教练某月的收入和支出
      async getAccountFlow() {
        let that = this;
        let url = '/deliver/app/teacher/getTeacherAccountFlow';
        let res = await http.get(url, {
          userCode: that.userCode,
          month: that.month
        });
        if (res.data.success) {
          that.priceDetails = res.data.data;
          that.priceDetails.outAmount = Number(that.priceDetails.outAmount).toFixed(2);
          that.priceDetails.inAmount = Number(that.priceDetails.inAmount).toFixed(2);
        }
      },

      goBillDetails(item) {
        if (item.orderId == '') {
          uni.navigateTo({
            url: `/pages/my/wallet/billDetails?info=${JSON.stringify(item)}`
          });
        } else {
          uni.navigateTo({
            url: `/pages/my/wallet/billDetails?orderId=${item.orderId}&type=${item.type}&teacherName=${this.teacherName}&merit_pay=${item.is_merit_pay}`
          });
        }
      },
      // 判断用户是否实名认证      //getRealName
      async goWallet() {
        let that = this;
        let res = await http.get('/mps/user/info/user/code', {
          userCode: this.userCode
        });
        console.log(res.data);
        if (res.data.success) {
          uni.navigateTo({
            url: `/pages/my/wallet/wallet?withdrawable=${this.withdrawalDetails.availableCashAmount}&userCode=${this.userCode}&key=${this.tabKey}`
          });
        } else {
          uni.showModal({
            title: '提示',
            content: '实名认证未完成，前往认证',
            showCancel: false,
            success: function (res) {
              if (res.confirm) {
                uni.redirectTo({
                  url: '/authen/authen?userCode=' + that.userCode
                });

                // uni.redirectTo({
                //   url: '/splitContent/authen/webview?url=' + encodeURIComponent(_this.signurl)
                // });
              } else if (res.cancel) {
                console.log('用户点击取消');
              }
            }
          });
        }
      }
    }
  };
</script>
<style>
  page {
    background-color: #fafcfe;
  }
</style>

<style lang="scss" scoped>
  .hear_img {
    position: absolute;
    top: 0;
    width: 100%;
    height: 590rpx;
    z-index: 99;
  }

  .popup-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    width: 500rpx;
    height: 400rpx;
    padding: 20rpx;
    border-radius: 20rpx;
    background: #fff;
  }

  .tab-bg {
    // background-color: #f3f8fc;
    position: fixed;
    top: 140rpx;
    width: 100%;
    height: 100rpx;
    display: flex;
    justify-content: center;
    z-index: 100;
  }

  .nav-title {
    font-weight: bold;
    width: 100%;
    font-size: 34rpx;
    padding-top: 80rpx;
    position: fixed;
    left: 40%;
    z-index: 999;
  }

  .status_bar {
    position: relative;
  }

  .title-icon {
    position: absolute;
    top: 0;
    left: -270rpx;
  }

  .wallet-bgc {
    width: 100%;
    height: 650rpx;
  }

  .bill {
    width: 92%;
    position: absolute;
    top: 640rpx;
    padding: 0 30rpx;
    padding-bottom: 30rpx;
  }

  .border-b {
    border-bottom: 1px solid #eeeeee;
  }

  // /deep/.u-tabs__wrapper__nav__line{
  // 	left:-9rpx !important;
  // }
  /deep/ .u-tabs__wrapper__nav__item__text {
    font-size: 14px !important;
  }

  .amount {
    color: #fff;
    width: 84%;
    position: absolute;
    top: 300rpx;
    left: 60rpx;
  }

  .balance {
    position: absolute;
    top: -20rpx;
    right: -40rpx;
  }

  .balance_explain {
    position: absolute;
    top: 0;
    width: 355rpx;
    height: 300rpx;
    border-radius: 15rpx;
    background-color: #fff;
    padding: 30rpx;
    right: -460rpx;
    z-index: 9;
    box-shadow: 0rpx 3rpx 10rpx #c7c7c7;
  }

  .total_money {
    position: absolute;
    top: -20rpx;
    // right: 70rpx;
    left: 90rpx;
  }

  .order-style-width {
    width: 420rpx;
  }

  .total_explain {
    position: absolute;
    top: 50rpx;
    right: -240rpx;
    width: 360rpx;
    height: 70rpx;
    line-height: 70rpx;
    background-color: #fff;
    border-radius: 10rpx;
    box-shadow: 0rpx 3rpx 10rpx #c7c7c7;
    padding: 0 15rpx;
  }

  .withdrawal_explain {
    position: absolute;
    top: 50rpx;
    right: -70rpx;
    width: 230rpx;
    height: 70rpx;
    line-height: 70rpx;
    text-align: center;
    background-color: #fff;
    border-radius: 10rpx;
    box-shadow: 0rpx 3rpx 10rpx #c7c7c7;
  }

  .taking {
    position: absolute;
    top: -20rpx;
    // right: 70rpx;
    left: 470rpx;
  }

  .money {
    font-size: 46rpx;
  }

  .withdrawal {
    width: 170rpx;
    height: 60rpx;
    color: #2f8c70;
    line-height: 60rpx;
    text-align: center;
    border-radius: 30rpx;
    background-image: linear-gradient(to right, #f2f8f6, #becec7);
  }

  .border-t {
    border-top: 1px solid #7ab09f;
  }

  /deep/ .u-toolbar {
    border-bottom: 1px solid #e0e0e0;
  }

  /deep/ .u-line-1 {
    line-height: 88rpx !important;
    background-color: #f4f4f4 !important;
  }

  /deep/ .u-picker__view {
    height: 440rpx !important;
  }

  /deep/ .u-picker__view__column {
    border-radius: 12rpx;
  }

  /deep/ .u-popup__content {
    border-radius: 12rpx;
    margin: 0 20rpx 20rpx 20rpx;
  }

  /deep/ .u-picker__view__column__item {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 30rpx !important;
    margin-left: 10rpx;
  }

  /deep/ .u-picker__view__column__item {
    margin-left: 0 !important;
  }

  .c-67C23A {
    color: green;
  }

  .c-E6A23C {
    color: #e6a23c;
  }

  .img_s {
    width: 160rpx;
    height: 160rpx;
  }

  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .export-btn-style {
    position: fixed;
    bottom: 15px;
    left: 50%;
    z-index: 999;
    transform: translateX(-50%);
    width: 580rpx;
    line-height: 80rpx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 35rpx;
    text-align: center;
    color: #ffffff;
    font-size: 30rpx;
  }

  .copy-excol-popup {
    background: #fff;
    text-align: center;
    padding: 38rpx 20rpx 20rpx 20rpx;
    border-radius: 15rpx;
    width: 500rpx;
    line-height: 50rpx;

    .btn-style {
      margin-top: 50rpx;
      display: inline-block;
      padding: 0 25rpx;
      line-height: 65rpx;
      background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
      border-radius: 15rpx;
      text-align: center;
      color: #ffffff;
      font-size: 26rpx;
    }

    .left-btn-style {
      color: #1d755c;
      background: #fff;
      border: 2rpx solid #1d755c;
    }

    .scrollView {
      overflow: hidden;
    }
  }
</style>
