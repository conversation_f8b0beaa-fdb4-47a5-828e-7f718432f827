<template>
  <view class="p-30">
    <view class="ptb-20 bg-ff radius-15" v-if="!signContractStatus">
      <!-- <u-steps v-if="signContractStatus" current="4" activeColor="#ec7532" inactiveColor="#D8D8D8" :dot="true">
				<u-steps-item title="实名认证"></u-steps-item>
				<u-steps-item title="绑定银行卡"></u-steps-item>
				<u-steps-item title="绑定手机号"></u-steps-item>
				<u-steps-item title="电子签约"></u-steps-item>
			</u-steps> -->
      <u-steps :current="active" activeColor="#ec7532" inactiveColor="#D8D8D8" :dot="true">
        <u-steps-item title="实名认证"></u-steps-item>
        <u-steps-item title="绑定银行卡"></u-steps-item>
        <u-steps-item title="绑定手机号"></u-steps-item>
        <u-steps-item title="电子签约"></u-steps-item>
      </u-steps>
      <!-- <van-steps :steps="steps" :active="active" active-color="#006658" inactive-color="#ccc" /> -->
    </view>
    <view class="mt-20 bg-ff" v-if="!signContractStatus" :style="{ height: useHeight + 'rpx' }" style="position: relative">
      <!-- 第一步实名认证 -->
      <view v-if="active == 0">
        <form @submit="submit_s">
          <view class="plr-30 bg-ff radius-20">
            <view class="ptb-30 b-db flex">
              <view class="titleleft f-30">登录用户</view>
              <view></view>
              <input placeholder-style="color:#999" class="flex-box" :value="mobile ? mobile : ''" placeholder="手机号" />
            </view>
            <view class="ptb-30 b-db flex">
              <view class="titleleft f-30">姓名</view>
              <input placeholder-style="color:#999" class="flex-box f-30" placeholder="请输入您的姓名" name="realName" />
            </view>
            <view class="ptb-30 b-db flex">
              <view class="titleleft f-30">身份证</view>
              <input placeholder-style="color:#999" class="flex-box f-30" type="idcard" placeholder="请输入您的身份证号" name="identityNo" />
            </view>
          </view>
          <view class="ptb-30 plr-20 f-26 c-99 flex-a-c">
            <label class="radio" style="display: inline-block; transform: scale(0.6)">
              <radio value="r1" :checked="isChecked" color="#1D755C" @click="changeischecked" />
            </label>
            我已阅读并同意
            <view class="bg" @click="toUserServer">《用户服务协议》</view>
            <text>、</text>
            <view class="bg" @click="toPrivacy">《隐私协议》</view>
          </view>
          <view class="m-45">
            <button class="nextstep" form-type="submit" :disabled="disabled">下一步</button>
          </view>
        </form>
      </view>
      <!-- 绑定银行卡 -->
      <view v-if="active == 1">
        <!-- <view > -->
        <form @submit="submit_bank">
          <view class="plr-30 bg-ff radius-20">
            <view class="ptb-30 b-db flex">
              <view class="titleleft f-30">银行卡号</view>
              <view></view>
              <input class="flex-box f-30" :value="bankCard.cardNo" placeholder="请输入银行卡号" name="bankCard" @input="step2card" />
            </view>
            <view class="ptb-30 b-db flex">
              <view class="titleleft f-30">预留手机</view>
              <input class="flex-box f-30" :value="bankCard.cardPhone" type="number" placeholder="请输入银行卡预留手机号" @input="step2phone" name="mobile" />
            </view>
            <view class="ptb-30 b-db flex">
              <view class="titleleft f-30">验证码</view>
              <input class="flex-box f-30" :value="bankCard.verificationCode" placeholder="请输入您的验证码" type="number" name="verificationCode" @input="stepCode" />
              <view class="getcode" v-if="!yanStatus" @tap="getcodes()">获取验证码</view>
              <view class="getcode" v-else>{{ minute }}s后获取</view>
            </view>
          </view>
          <view class="m-45">
            <button class="nextstep" form-type="submit" :disabled="disabled">下一步</button>
            <!-- <button class="nextstep1" @click="changeToEnter">解除实名认证</button> -->
          </view>
        </form>
      </view>
      <!-- 绑定支付手机号 -->
      <view v-if="active == 2">
        <form @submit="bindpayphone">
          <view class="plr-30 bg-ff radius-20">
            <view class="ptb-30 b-db flex">
              <view class="titleleft f-30">手机号</view>
              <input class="flex-box f-30" placeholder="手机号" :value="telbangding.phone" @input="userPhoneinput" name="phone" />
            </view>
            <view class="ptb-30 b-db flex">
              <view class="titleleft f-30">验证码</view>
              <input @input="codeInput" class="flex-box f-30" placeholder="请输入验证码" type="number" name="verificationCode" />
              <view class="getcode" v-if="!payyanStatus" @tap="getpaycodes()">获取验证码</view>
              <view class="getcode" v-else>{{ minute }}s后获取</view>
            </view>
          </view>
          <view class="m-45">
            <button class="nextstep" form-type="submit" :disabled="disabled">完成</button>
            <!-- <button class="nextstep1" @click="changeToEnter">解除实名认证</button> -->
          </view>
        </form>
      </view>
      <!-- 签约 -->
      <view class="determine" v-if="active == 3">
        <!-- #ifdef APP-PLUS -->
        <view class="f-30 t-c prompt">请前往网页完成电子签约，开通提现功能</view>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
        <view class="f-30 t-c prompt">请前往通联小程序完成电子签约，开通提现功能</view>
        <!-- #endif -->
        <view>
          <view class="determine-btn" @tap="tosign">确定</view>
          <!-- <button class="nextstep1" @click="changeToEnter">解除实名认证</button> -->
        </view>
      </view>
    </view>
    <view class="" v-if="signContractStatus">
      <view class="">
        <u-cell-group :border="false">
          <u-cell title="实名认证状态" :value="nameStatus"></u-cell>
          <!-- <u-cell title="个人信息" isLink @click="goUrl('/authen/user?userCode=' + userCode)"></u-cell> -->
        </u-cell-group>
      </view>
      <!--      <view class="">
        <u-cell-group :border="false">
          <u-cell title="解除实名认证" value="" isLink @click="changeToEnterprisea"></u-cell>
        </u-cell-group>
      </view> -->
    </view>
    <uni-popup ref="addPopup" type="center" :is-mask-click="false" :safe-area="false">
      <view class="dialogBG pt-25 pb-45 pl-20 pr-20">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="flex-s" style="margin-bottom: 60rpx">
            <view class="" style="width: 42rpx; height: 42rpx"></view>
            <view style="font-size: 30rpx; font-weight: 650; flex: 1; text-align: center">温馨提示</view>
            <image @click="addCancel" style="width: 42rpx; height: 42rpx" src="/static/images/icon_close.png" mode="aspectFill"></image>
          </view>
          <view class="flex-center1" style="margin-bottom: 30rpx">您确定解除实名制绑定吗?</view>
          <view class="flex-center2" style="margin-bottom: 30rpx">每个身份证只能解除绑定三次</view>
          <view class="flex-center2" style="margin-bottom: 30rpx">请您务必确认好!!!</view>
          <view class="flex-s">
            <view class="common-sure-btn" @click="addSure">确定</view>
            <view class="common-cancel-btn" @click="addCancel">取消</view>
          </view>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="addPopup1" type="center" :is-mask-click="false" :safe-area="false">
      <view class="dialogBG pt-25 pb-45 pl-20 pr-20">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="flex-s" style="margin-bottom: 60rpx">
            <view class="" style="width: 42rpx; height: 42rpx"></view>
            <view style="font-size: 30rpx; font-weight: 650; flex: 1; text-align: center">温馨提示</view>
            <image @click="addCancel" style="width: 42rpx; height: 42rpx" src="/static/images/icon_close.png" mode="aspectFill"></image>
          </view>
          <view class="flex-center1" style="margin-bottom: 30rpx">您确定解除实名制绑定吗?</view>
          <view class="flex-center2" style="margin-bottom: 30rpx">每个身份证只能解除绑定三次</view>
          <view class="flex-center2" style="margin-bottom: 30rpx">请您务必确认好!!!</view>
          <view class="flex-s">
            <view class="common-sure-btn" @click="addSure1">确定</view>
            <view class="common-cancel-btn" @click="addCancel1">取消</view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import Util from '@/utils/utils.js';
  let count;
  export default {
    data() {
      return {
        nameStatus: '', //实名认证状态
        signContractStatus: false, // 实名认证是否完成
        // 实名认证
        realName: {
          bizUserId: '',
          identityNo: '',
          name: '',
          providerType: 'ALLIN_PAY'
        },

        // 银行卡
        bankCard: {
          cardNo: '', // 银行卡
          cardPhone: '', // 预留手机号
          verificationCode: '', // 验证码
          bizUserId: '',
          providerType: 'ALLIN_PAY',
          tranceNum: ''
        },

        // 银行卡验证码
        minuteList: {
          cardNo: '',
          cardPhone: '',
          providerType: 'ALLIN_PAY'
        },

        // // 手机号验证码
        // telApiList: {
        // 	bizUserId: "",
        // 	phone:"",
        // 	verificationCodeType:""
        // },

        payStatus: false, // 是否绑定支付标识

        // 绑定手机号
        telbangding: {
          verificationCode: null,
          phone: '',
          bizUserId: '',
          tranceNum: ''
        },

        // 签约跳转
        contractList: {
          backStyle: 'DUBBO',
          source: 1,
          bizUserId: ''
        },
        contract: null,
        urls: 'http://localhost:9529/#/layout/personMe/realName',

        pageShow: true,
        info: null,
        disabled: false,
        yanStatus: false,
        payyanStatus: false,
        minute: 60,
        // bankCard: '', //第二步银行卡号
        // mobile1: '', //第二步手机号
        userCode: '',
        tranceNum: '', // 第三步验证手机号返的
        // userPhone: '',
        // tranceNum2: '', //流水号
        signurl: '', // 签约跳转地址

        active: 0,
        steps: [
          {
            text: '实名认证'
          },
          {
            text: '绑定银行卡'
          },
          {
            text: '绑定手机号'
          },
          {
            text: '电子签约'
          }
        ],

        useHeight: 0, //除头部之外高度
        imgHost: 'https://document.dxznjy.com/',
        isChecked: false,
        mobile: ''
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 220;
        }
      });
    },
    onLoad(options) {
      this.userCode = options.userCode;
    },
    onShow() {
      // this.homeData()
      this.getPersonalList();
      this.getRealName();
    },
    methods: {
      goUrl(url) {
        let token = uni.getStorageSync('token');
        if (token) {
          uni.navigateTo({
            url: url
          });
        } else {
          this.$tool.toLoginPage(2);
        }
      },
      async addSure1() {
        // console.log();
        let userCode = this.userCode;
        console.log(this.userCode);
        // let userRole=
        uni.$http.post(`/mps/user/info/user/unbindOrNot?userCode=${userCode}&userRole=Member`).then((res) => {
          if (res.data.success) {
            uni.navigateTo({
              url: '/authen/realName?userCode=' + userCode
            });
          }
        });
      },
      addCancel1() {
        this.$refs.addPopup1.close();
      },
      //验证是否可以
      changeToEnterprisea() {
        this.$refs.addPopup1.open();
      },
      async addSure() {
        uni.$http
          .post(`/mps/user/info/user/submitUnbind?userCode=${this.userCode}&isUnbindUnverified=1&unbindSource=助教端&userRole=Member&source=2`)
          .then(({ data }) => {
            if (data.success) {
              this.homeData();
              this.getRealName();
            } else {
              return uni.showModal({
                title: '温馨提示',
                content: data.message,
                showCancel: false
              });
            }
          })
          .catch((err) => {});
      },
      addCancel() {
        this.$refs.addPopup.close();
      },
      // 中途解绑
      changeToEnter() {
        this.$refs.addPopup.open();
      },
      async goUrla(url) {
        let token = uni.getStorageSync('token');
        if (token) {
          let res = await uni.$http.get('/mps/account/list/login/code/byCode', {
            userCode: this.userCode
          });
          uni.navigateTo({
            url: '/authen/realName?userCode=' + this.userCode
          });
          // if (res.data.success) {
          // 	let a
          // 	for (let i = 0; i < res.data.data.length; i++) {
          // 		if (res.data.data[i].userCode == this.userCode) {
          // 			a  = res.data.data[i];
          // 		}
          // 	}
          // 		// this.withdrawalDetails.totalMoney = Util.Fen2Yuan(this.withdrawalDetails.totalMoney || 0); // 总收益
          // 		let money = Util.Fen2Yuan(a.availableCashAmount || 0); // 可提现
          // 		if(money){
          // 			uni.navigateTo({
          // 				url: '/authen/canNot?userCode='+ this.userCode
          // 			})
          // 		}else {
          // 			uni.navigateTo({
          // 				url: '/authen/realName?userCode='+ this.userCode
          // 			})
          // 		}
          // 		// this.withdrawalDetails.paymentIn = Util.Fen2Yuan(this.withdrawalDetails.paymentIn || 0); // 已结算
          // 		// this.getWithdrawal()

          // }
        } else {
          this.$tool.toLoginPage(2);
        }
      },
      // 获取个人信息(账号，姓名)
      async getPersonalList() {
        let { data } = await uni.$http.get('/deliver/app/teacher/info');
        this.info = data;
        this.mobile = data.data.mobile;
      },
      //姓名格式化
      formatName2(str) {
        if (str) {
          return new Array(str.length).join('*') + str.substr(str.length - 1, 1);
        }
      },
      async getRealName() {
        // 先获取usrcode
        if (!this.info) {
          setTimeout(() => {
            this.getRealName();
          }, 200);
          return;
        }
        console.log(this.info, 33333);
        let res = '';
        // 判断用户是否实名认证
        if (this.userCode) {
          res = await uni.$http.get('/mps/user/info/user/code', {
            userCode: this.userCode
          });
        }
        this.contract = res.data.data;
        this.realName.bizUserId = res.data.data.bizUserId;
        if (res.data.success) {
          if (res.data.data.signContractStatus == 1) {
            this.nameStatus = '已认证(' + this.formatName2(res.data.data.realName) + ')';
            this.signContractStatus = true; // 实名认证完成
            if (res.data.data.unbindStatus === '0') {
              uni.navigateTo({
                url: `/authen/realstatus?userCode=${res.data.data.userCode}&status=0&id=${res.data.data.ystItemId}`
              });
            } else if (res.data.data.unbindStatus == 2) {
              uni.navigateTo({
                url: `/authen/realstatus?userCode=${res.data.data.userCode}&status=2&remark=${res.data.data.approveRemake}`
              });
            }
            this.useHeight = this.useHeight + 40;
          }
        }

        // 判断跳转
        if (res.data.success && res.data.data != null) {
          if (res.data.data.certStatus == '1' && res.data.data.signContractStatus != 1) {
            if (res.data.data.bindCardStatus != 1) {
              //已经通过实名认证 未绑定银行卡
              this.active = 1;
              uni.showToast({
                icon: 'none',
                title: '您已完成实名认证,请绑定银行卡信息',
                duration: 2000
              });
            } else if (res.data.data.bindPhoneStatus == 0 && res.data.data.signContractStatus != 1) {
              //未绑定手机
              this.active = 2;
              uni.showToast({
                icon: 'none',
                title: '您已完成实名认证,请绑定手机号',
                duration: 2000
              });
            } else if (res.data.data.bindPhoneStatus == 1 && res.data.data.signContractStatus != 1) {
              this.active = 3;
              uni.showToast({
                icon: 'none',
                title: '您已完成实名认证,请完成电子签约',
                duration: 2000
              });
            }
          }
        }
      },

      // 第四步去签约
      async tosign() {
        let _this = this;
        _this.contractList.bizUserId = _this.realName.bizUserId;
        let res = await uni.$http.get('/mps/auth/sign/contract', _this.contractList);
        _this.signurl = res.data.data;
        uni.setStorageSync('signurl', res.data.data);
        console.log(res);
        // #ifdef MP-WEIXIN
        uni.navigateToMiniProgram({
          appId: 'wxc46c6d2eed27ca0a',
          path: '/pages/merchantAddress/merchantAddress',
          extraData: {
            targetUrl: _this.signurl
          },
          envVersion: 'release',
          success(res) {
            uni.navigateBack();
            // 打开成功
          }
        });
        // #endif
        // #ifdef APP-PLUS
        uni.navigateTo({
          url: '/authen/webview?url=' + _this.signurl
        });
        // #endif
      },
      // // 第四步去签约
      //      async tosign() {
      //        let _this = this;
      //        _this.contractList.bizUserId = _this.realName.bizUserId;
      //        let res = await httpUser.get('mps/auth/sign/contract', _this.contractList);
      //        _this.signurl = res.data.data;
      //        console.log(res);
      //        // #ifdef MP-WEIXIN
      //        uni.navigateToMiniProgram({
      //          appId: 'wxc46c6d2eed27ca0a',
      //          path: '/pages/merchantAddress/merchantAddress',
      //          extraData: {
      //            targetUrl: _this.signurl
      //          },
      //          envVersion: 'release',
      //          success(res) {
      //            uni.navigateBack();
      //            // 打开成功
      //          }
      //        });
      //        // #endif
      //        // #ifdef APP-PLUS
      //        uni.navigateTo({
      //          url: '/splitContent/authen/webview?url=' + _this.signurl
      //        });
      //        // #endif
      //      },
      // 绑定手机号input框
      userPhoneinput(e) {
        this.telbangding.phone = e.detail.value;
      },

      // 绑定手机号input框
      codeInput(e) {
        this.telbangding.verificationCode = e.detail.value;
      },

      // 绑定手机号验证码
      async getpaycodes() {
        let _this = this;
        if (!Util.isMobile(_this.telbangding.phone)) {
          return this.showError('请输入手机号');
        }
        // _this.paymentId()
        _this.getSmscode();
      },

      // 第三步绑定手机验证码
      async getSmscode() {
        let _this = this;
        let telApiList = {
          bizUserId: _this.realName.bizUserId,
          phone: _this.telbangding.phone
        };
        uni.showLoading({
          title: '获取中，请稍后'
        });
        let res = await uni.$http.get('/mps/auth/bind/phone/sms/use/bank', telApiList);
        console.log(res);
        uni.hideLoading();
        if (res.data.success) {
          _this.payyanStatus = true;
          _this.countdown();
          uni.showToast({
            title: '验证码发送成功，请注意接收！'
          });
          _this.tranceNum = res.data.data.tranceNum;
          _this.payStatus = true;
        } else {
          _this.$util.alter(res.data.message);
        }
      },

      // 判断用户是否绑定支付标识
      // async paymentId() {
      // 	let _this = this
      // 	let request = await http.get('/mps/auth/bind/apply/acct', {
      // 		bizUserId: this.realName.bizUserId
      // 	})
      // 	console.log(request);
      // 	// 绑定支付标识
      // 	if (request.data.data) {
      // 		let telApiList = {
      // 			bizUserId: _this.realName.bizUserId,
      // 			phone: _this.telbangding.phone
      // 		}
      // 		uni.showLoading({
      // 			title: '获取中，请稍后'
      // 		})
      // 		let res = await http.get('/mps/auth/bind/phone/sms/use/bank', telApiList)
      // 		console.log(res);
      // 		uni.hideLoading()
      // 		if (res.data.success) {
      // 			_this.payyanStatus = true;
      // 			_this.countdown()
      // 			uni.showToast({
      // 				title: '验证码发送成功，请注意接收！'
      // 			})
      // 			this.tranceNum = res.data.data.tranceNum;
      // 			_this.payStatus = true
      // 		}
      // 	} else {
      // 		let telApiList = {
      // 			bizUserId: _this.realName.bizUserId,
      // 			phone: _this.telbangding.phone,
      // 			verificationCodeType: 9
      // 		}
      // 		uni.showLoading({
      // 			title: '获取中，请稍后'
      // 		})
      // 		let res = await http.put('/mps/auth/send/sms', telApiList)
      // 		console.log(res);
      // 		uni.hideLoading()
      // 		if (res.data.success) {
      // 			_this.payyanStatus = true
      // 			_this.countdown()
      // 			uni.showToast({
      // 				title: '验证码发送成功，请注意接收！'
      // 			})
      // 			_this.payStatus = false
      // 		} else {
      // 			_this.$util.alter(res.data.message);
      // 		}
      // 	}
      // },

      // 第三步绑定手机号
      async bindpayphone(e) {
        let _this = this,
          values = e.detail.value;
        if (!Util.isMobile(_this.telbangding.phone)) {
          return this.showError('请输入手机号');
        }
        if (!values.verificationCode) {
          return this.showError('请输入验证码');
        }
        if (_this.disabled) {
          return;
        }
        _this.telbangding.bizUserId = this.realName.bizUserId;
        _this.telbangding.tranceNum = _this.tranceNum;
        uni.showLoading({
          title: '提交中'
        });
        _this.disabled = true;
        let res = await uni.$http.put('/mps/auth/bind/phone/use/bank', _this.telbangding);
        console.log(res);
        _this.disabled = false;
        uni.hideLoading();
        if (res.data.success) {
          setTimeout(function () {
            clearInterval(count);
            _this.minute = 60;
            _this.payyanStatus = false;
            _this.yanStatus = false;
            _this.homeData();
          }, 2000);
          _this.active = 3;
        } else {
          _this.$util.alter(res.data.message);
        }
        // if (_this.payStatus) {} else {
        // 	let res = await http.put('/mps/auth/bind/phone', _this.telbangding)
        // 	console.log(res);
        // 	_this.disabled = false
        // 	uni.hideLoading()
        // 	if (res.data.success) {

        // 		setTimeout(function() {
        // 			clearInterval(count);
        // 			_this.minute = 60
        // 			_this.payyanStatus = false
        // 			_this.yanStatus = false
        // 			_this.homeData()
        // 		}, 2000)
        // 		_this.active = 3
        // 	} else {
        // 		_this.$util.alter(res.data.message);
        // 	}
        // }
      },

      // 第二步绑定银行卡
      async submit_bank(e) {
        let _this = this,
          values = e.detail.value;
        console.log(values);
        console.log('//////////////////');
        if (!Util.checkCard(_this.bankCard.cardNo)) {
          return this.showError('请输入银行卡号');
        }
        if (!_this.bankCard.cardPhone) {
          return this.showError('请输入预留手机号');
        }
        if (!values.verificationCode) {
          return this.showError('请输入验证码');
        }
        if (_this.disabled) {
          return;
        }
        _this.bankCard.bizUserId = _this.realName.bizUserId;
        uni.showLoading({
          title: '提交中'
        });
        _this.disabled = true;
        let res = await uni.$http.put('/mps/auth/bind/card/confirm', _this.bankCard);
        _this.disabled = false;
        if (res.data.success) {
          _this.active = 2;

          setTimeout(function () {
            clearInterval(count);
            _this.minute = 60;
            _this.yanStatus = false;
            _this.homeData();
          }, 2000);
        } else {
          _this.active = 1;
          _this.$util.alter(res.data.message);
        }
        _this.disabled = false;
        uni.hideLoading();
      },

      step2card(e) {
        this.bankCard.cardNo = e.detail.value;
      },
      step2phone(e) {
        this.bankCard.cardPhone = e.detail.value;
      },
      stepCode(e) {
        this.bankCard.verificationCode = e.detail.value;
      },

      // 银行卡获取验证码
      async getcodes() {
        let _this = this;
        if (!Util.checkCard(_this.bankCard.cardNo)) {
          return this.showError('请输入银行卡号');
        }
        if (!_this.bankCard.cardPhone) {
          return this.showError('请输入预留手机号');
        }
        if (_this.yanStatus) {
          return;
        }
        uni.showLoading({
          title: '获取中，请稍后'
        });
        _this.minuteList.bizUserId = _this.realName.bizUserId;
        _this.minuteList.cardNo = _this.bankCard.cardNo;
        _this.minuteList.cardPhone = _this.bankCard.cardPhone;
        let res = await uni.$http.put('/mps/auth/bind/card', _this.minuteList);
        console.log(res);
        uni.hideLoading();
        if (res.data.success) {
          _this.yanStatus = true;
          _this.countdown();
          _this.bankCard.tranceNum = res.data.data;
          console.log(_this.bankCard.tranceNum);
          uni.showToast({
            title: '验证码发送成功，请注意接收！'
          });
        } else {
          _this.$util.alter(res.data.message);
        }
      },
      // 倒计时
      countdown() {
        let _this = this;
        count = setInterval(function () {
          //倒计时
          var minute = _this.minute;
          minute--;
          _this.minute = minute;
          _this.yanStatus = true;
          if (minute == 0) {
            clearInterval(count);
            _this.minute = 60;
            _this.payyanStatus = false;
            _this.yanStatus = false;
          }
        }, 1000);
      },

      // 第一步实名认证
      async submit_s(e) {
        let _this = this,
          values = e.detail.value;
        if (!Util.isName(values.realName)) {
          // return (_this.active = 0);
          return this.showError('姓名不符合要求');
        }
        if (!Util.isCardID(values.identityNo)) {
          // return (_this.active = 0);
          return this.showError('身份证号不符合要求');
        }
        if (_this.disabled) {
          return;
        }
        if (!this.isChecked) {
          this.$util.alter('请阅读并勾选下方协议');
          return false;
        }
        uni.showLoading({
          title: '提交中'
        });
        _this.disabled = true;
        _this.realName.name = values.realName;
        _this.realName.identityNo = values.identityNo;
        let res = await uni.$http.put('/mps/auth/verified', this.realName);
        console.log(res);
        _this.disabled = false;
        uni.hideLoading();
        if (res.data.success) {
          _this.realName.identityNo = null;
          _this.bankCard.verificationCode = null;
          _this.active = 1;
          setTimeout(function () {
            _this.homeData();
          }, 2000);
        } else {
          _this.$util.alter(res.data.message);
        }
      },

      // 同意已阅读用户服务协议
      changeischecked() {
        this.isChecked = !this.isChecked;
        // if (this.mobile.length == 11 && this.smsCode.length == 6 && this.isChecked) {
        //     this.lastactive = true
        // } else {
        //     this.lastactive = false
        // }
      },

      //
      async homeData() {
        let _this = this;
        let userCode = this.userCode;
        console.log(userCode, '****************');
        if (userCode) {
          const res = await uni.$http.get('/mps/user/info/user/code', {
            userCode: userCode
          });
          if (res) {
            _this.pageShow = false;
            _this.info = res.data;
            console.log(444444);
            // 最后一步获取签约地址
            // if (res.data.isBindPayPhone == 1 && res.data.signContractStatus == 0) {
            // 	_this.active = 2
            // 	uni.setNavigationBarTitle({
            // 		title: '电子签约'
            // 	})
            // 	_this.signData()
            // }
            // if (res.data.signContractStatus == 1) {
            // 	_this.active = 3
            // }
          }
        }
      },
      // async signData() {
      // 	let _this = this
      // 	const res = await http.get('mps/auth/sign/contract',_this.contractList)
      // 	console.log(res);
      // },
      showError(msg) {
        return new Promise((resolve, reject) => {
          uni.showModal({
            title: '友情提示',
            content: msg,
            showCancel: false,
            success(res) {
              resolve(res);
            }
          });
        });
      },
      showSuccess(msg) {
        return new Promise((resolve, reject) => {
          uni.showToast({
            title: msg,
            icon: 'success',
            mask: true,
            duration: 1500,
            success() {
              setTimeout(function () {
                resolve();
              }, 1500);
            }
          });
        });
      },
      showMsg(msg) {
        return new Promise((resolve, reject) => {
          uni.showToast({
            title: msg,
            icon: 'none',
            mask: true,
            duration: 1500,
            success() {
              setTimeout(function () {
                resolve();
              }, 1500);
            }
          });
        });
      },
      toUserServer() {
        uni.navigateTo({
          url: '/authen/useragree'
        });
      },
      toPrivacy() {
        uni.navigateTo({
          url: '/authen/Privacyagree'
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .determine {
    position: absolute;
    bottom: 40rpx;
    width: 100%;
  }

  .prompt {
    margin-bottom: 560rpx;
    padding: 0 120rpx;
  }

  .nextstep {
    width: 586rpx;
    height: 80rpx;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    margin: 30rpx auto;
    text-align: center;
    line-height: 80rpx;
    color: #fff;
    border-radius: 45rpx;
  }

  .nextstep1 {
    width: 586rpx;
    height: 80rpx;
    border: 2rpx solid #2e896f;
    margin: 30rpx auto;
    text-align: center;
    line-height: 80rpx;
    color: #2e896f;
    border-radius: 45rpx;
  }

  .determine-btn {
    width: 586rpx;
    height: 80rpx;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    margin: 0 auto;
    text-align: center;
    line-height: 80rpx;
    color: #fff;
    border-radius: 45rpx;
  }

  .titleleft {
    width: 150upx;
  }

  .getcode {
    background-color: #2e896f;
    color: #fff;
    padding: 8upx 12upx;
    font-size: 28upx;
    border-radius: 25upx;
  }

  .circle {
    width: 160upx;
    height: 160upx;
    background-color: #fff;
    border-radius: 100upx;
    border: 8upx solid #eee;
    color: #006658;
    line-height: 160upx;
    text-align: center;
    margin: 30upx auto;
  }

  /deep/.u-text__value--main {
    color: #000 !important;
    font-size: 26rpx !important;
  }

  /deep/.u-text__value--content {
    color: #999 !important;
    font-size: 26rpx !important;
  }

  .bg {
    color: #eb673a;
  }

  .complete_img {
    width: 100rpx;
    height: 100rpx;
  }

  // .flex-s {
  //   display: flex;
  //   flex-direction: column;
  //   align-items: center;
  //   justify-content: center;
  // }

  /deep/.u-steps-item__wrapper__dot {
    width: 30rpx !important;
    height: 30rpx !important;
  }

  /deep/.u-steps-item__content {
    margin-top: 20rpx !important;
  }

  /deep/.u-cell-group {
    background-color: #fff;
    border-radius: 20rpx;
    margin: 20rpx;
  }

  .dialogBG {
    margin: 200rpx 30rpx 30rpx 30rpx;
    background-color: #fff;
    border-radius: 14rpx;
  }

  .dialog-all {
    width: 560rpx;
    position: relative;
  }

  .dialog-all image {
    width: 100%;
    height: 100%;
  }

  .input-border {
    height: 80rpx;
    border-radius: 14rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.2);
  }

  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .flex-center1 {
    // display: flex;
    text-align: center;
    // align-items: center;
    // justify-content: center;
    color: #000;
    font-size: 30rpx;
    padding: 0 40rpx;
    line-height: 1.2;
  }

  .flex-center2 {
    margin-top: 20rpx;
    text-align: center;
    padding: 0 40rpx;
    // color: #f99a4b;
    color: red;
    font-size: 28rpx;
  }

  .common-sure-btn {
    width: 250rpx;
    height: 80rpx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45rpx;
    font-size: 30rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 25rpx;
  }

  .common-cancel-btn {
    width: 250rpx;
    height: 80rpx;
    border-radius: 45rpx;
    border: 1rpx solid #2e896f;
    font-size: 30rpx;
    color: #2e896f;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 25rpx;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 100rpx;
    z-index: -1;
  }
</style>
