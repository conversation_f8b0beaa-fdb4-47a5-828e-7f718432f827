/**
 * 图片处理工具类
 * 使用 uni-app 原生 API，避免依赖 HTML5+ 的 zip 模块
 */

/**
 * 压缩图片
 * @param {string} src 图片路径
 * @param {object} options 压缩选项
 * @returns {Promise<string>} 压缩后的图片路径
 */
export function compressImage(src, options = {}) {
  return new Promise((resolve, reject) => {
    // 默认压缩选项
    const defaultOptions = {
      quality: 80,
      maxWidth: 800,
      maxHeight: 800
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    // 先获取图片信息
    uni.getImageInfo({
      src: src,
      success: (imageInfo) => {
        // 判断是否需要压缩
        if (imageInfo.width <= finalOptions.maxWidth && imageInfo.height <= finalOptions.maxHeight) {
          // 图片尺寸已经符合要求，不需要压缩
          resolve(src);
          return;
        }
        
        // 使用 uni.compressImage 进行压缩
        uni.compressImage({
          src: src,
          quality: finalOptions.quality,
          success: (compressResult) => {
            console.log('图片压缩成功:', compressResult);
            resolve(compressResult.tempFilePath);
          },
          fail: (error) => {
            console.log('图片压缩失败:', error);
            // 压缩失败时返回原图
            resolve(src);
          }
        });
      },
      fail: (error) => {
        console.log('获取图片信息失败:', error);
        // 获取图片信息失败时返回原图
        resolve(src);
      }
    });
  });
}

/**
 * 选择并压缩图片
 * @param {object} chooseOptions 选择图片的选项
 * @param {object} compressOptions 压缩选项
 * @returns {Promise<string>} 处理后的图片路径
 */
export function chooseAndCompressImage(chooseOptions = {}, compressOptions = {}) {
  return new Promise((resolve, reject) => {
    // 默认选择选项
    const defaultChooseOptions = {
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera']
    };
    
    const finalChooseOptions = { ...defaultChooseOptions, ...chooseOptions };
    
    uni.chooseImage({
      ...finalChooseOptions,
      success: async (res) => {
        try {
          const tempFilePath = res.tempFilePaths[0];
          const compressedPath = await compressImage(tempFilePath, compressOptions);
          resolve(compressedPath);
        } catch (error) {
          reject(error);
        }
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
}

/**
 * 拍照并压缩
 * @param {object} compressOptions 压缩选项
 * @returns {Promise<string>} 处理后的图片路径
 */
export function takePhotoAndCompress(compressOptions = {}) {
  return chooseAndCompressImage({
    sourceType: ['camera']
  }, compressOptions);
}

/**
 * 从相册选择并压缩
 * @param {object} compressOptions 压缩选项
 * @returns {Promise<string>} 处理后的图片路径
 */
export function chooseFromAlbumAndCompress(compressOptions = {}) {
  return chooseAndCompressImage({
    sourceType: ['album']
  }, compressOptions);
}

export default {
  compressImage,
  chooseAndCompressImage,
  takePhotoAndCompress,
  chooseFromAlbumAndCompress
};
