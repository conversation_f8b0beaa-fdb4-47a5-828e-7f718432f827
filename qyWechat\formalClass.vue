<template>
  <page-meta :page-style="'overflow:' + (showDialog ? 'hidden' : 'visible')"></page-meta>
  <view class="plr-30 pb-30">
    <u-navbar title="正式课" bgColor="#f3f8fc" placeholder>
      <view class="" slot="left">
        <u-icon name="arrow-left" size="24" bold color="#000" @click="goBack"></u-icon>
      </view>
      <view class="u-nav-slot" slot="center" style="font-weight: 600">{{ navBarTitle }}</view>
    </u-navbar>
    <view class="bg-ff plr-20 pb-40 radius-15 positionRelative">
      <block v-if="orderStatus.type == 1">
        <view class="flex-coum" :style="{ height: useHeight + 'rpx' }">
          <image :src="img1" style="width: 130rpx; height: 130rpx" mode="aspectFill"></image>
          <block v-if="!orderStatus.recvingTime && !orderStatus.teacherName && !orderStatus.teamName">
            <view class="mt-40" style="color: #666666; font-size: 30rpx">已被接单</view>
          </block>
          <block v-else>
            <view class="mt-40" style="color: #666666; font-size: 30rpx" v-if="orderStatus.teacherName">
              <text>已被</text>
              <text style="color: #000; font-weight: 700">{{ orderStatus.teacherName }}</text>
              <text>教练接单</text>
            </view>
            <view class="mt-40" style="color: #666666; font-size: 30rpx" v-if="orderStatus.teamName">
              <text>已被</text>
              <text style="color: #000; font-weight: 700">{{ orderStatus.teamName }}</text>
              <text>小组接单</text>
            </view>
            <view class="mt-40" style="color: #666666; font-size: 30rpx">接单时间：{{ orderStatus.recvingTime }}</view>
          </block>
        </view>
      </block>
      <block v-if="orderStatus.type == 2">
        <view class="flex-coum" :style="{ height: useHeight + 'rpx' }">
          <image :src="img2" style="width: 130rpx; height: 130rpx" mode="aspectFill"></image>
          <view class="mt-40" style="color: #666666; font-size: 30rpx; text-align: center">因未及时接单</view>
          <view class="mt-10" style="color: #666666; font-size: 30rpx; text-align: center">已被转移至其他交付中心</view>
        </view>
      </block>
      <block v-if="orderStatus.type == 3">
        <!-- <form id="#nform"> -->
        <view class="information ptb-30 borderB">
          <view style="width: 40%" class="f-30 bold">学员姓名：</view>
          <view style="width: 60%; color: #666666">{{ infolist.studentName }}</view>
        </view>
        <view class="information ptb-30 borderB">
          <view style="width: 40%" class="f-30 bold">学员编号：</view>
          <view style="width: 60%; color: #666666">{{ infolist.studentCode }}</view>
        </view>
        <view class="information ptb-30 borderB">
          <view style="width: 40%" class="f-30 bold">联系方式：</view>
          <view style="width: 60%; color: #666666">{{ telHide(infolist.mobile) }}</view>
        </view>
        <view class="information ptb-30 borderB">
          <view style="width: 40%" class="f-30 bold">课程类型：</view>
          <view style="width: 60%; color: #666666">{{ infolist.curriculumName }}</view>
        </view>
        <view class="information ptb-30 borderB">
          <view style="width: 40%" class="f-30 bold">年级：</view>
          <view style="width: 60%; color: #666666">{{ getGrade(infolist.grade) }}</view>
        </view>
        <view class="information ptb-30 borderB">
          <view style="width: 40%" class="f-30 bold">充值交付时长：</view>
          <view style="width: 60%; color: #666666">{{ infolist.rechargeHour }}</view>
        </view>

        <view class="ptb-15 borderB" v-if="!CurriculumCodeArr.includes(infolist.curriculumCode)">
          <view class="information">
            <view style="width: 73%" class="f-30 bold">课程规划：</view>
            <view class="uni-list-cell-db pr-20 positionRelative" @click="openCoursePlan" v-if="!isDisable">
              <view v-if="courseChoseList.length > 0" style="position: absolute; right: 20rpx">
                <!-- <text style="font-size: 30rpx; color: #2e896f; line-height: 42rpx">编辑</text> -->
              </view>
              <view class="flex-s" v-else>
                <view class="flex-a-c"><text style="float: left">请选择</text></view>
                <view class="time-icon">
                  <u-icon v-if="!isDisable" name="arrow-right" color="#c7c7c7" size="30"></u-icon>
                </view>
              </view>
            </view>
          </view>
          <view v-if="courseChoseList.length > 0 || isDisable">
            <view class="mt-30 mb-25" v-for="(item, index) in courseChoseList" :key="index">
              <view class="course-bg-view positionRelative flex-a-c">
                <view class="course">{{ item.courseName }}</view>
                <view class="first-class-vocabulary" v-if="item.isFirstWordBase">首节课词库</view>
              </view>
            </view>
          </view>
        </view>

        <view class="ptb-15 borderB">
          <view class="information">
            <view style="width: 73%" class="f-30 bold">上课时间：</view>
            <view class="uni-list-cell-db flex-s pr-20 flex-s positionRelative" @click="openTime" v-if="!isDisable || isEdit">
              <view v-if="comfireCourseData.length > 0 || isEdit" style="position: absolute; right: 20rpx">
                <!-- <text style="font-size: 30rpx; color: #2e896f; line-height: 42rpx">编辑</text> -->
              </view>
              <view class="flex-s" v-else>
                <view class="flex-a-c"><text style="float: left">请选择</text></view>
                <view class="time-icon">
                  <u-icon v-if="!isDisable" name="arrow-right" color="#c7c7c7" size="30"></u-icon>
                </view>
              </view>
            </view>
          </view>
          <view v-if="comfireCourseData.length > 0 || isDisable">
            <view class="mt-30 mb-25 text-more-view" v-for="(item, index) in comfireCourseData" :key="index">
              <view style="flex: 2" class="text-view">{{ getWeekName(item.week) }}</view>
              <view style="flex: 9; line-height: 50rpx" class="text-view">
                {{ getAllTimeShow(item.time) }}
              </view>
            </view>
          </view>
        </view>

        <view class="ptb-15 borderB" v-if="infolist.curriculumName == '鼎英语'">
          <view class="information">
            <view style="width: 73%" class="f-30 bold">复习时间：</view>
            <view class="uni-list-cell-db pr-20 positionRelative" @click="openReviewTime" v-if="!isDisable || isEdit">
              <view
                style="position: absolute; right: 20rpx"
                v-if="
                  (comfireReviewData.week != undefined && comfireReviewData.week.length > 0 && comfireReviewData.startTime != undefined && comfireReviewData.startTime != '') ||
                  isEdit
                "
              >
                <!-- <text style="font-size: 30rpx; color: #2e896f; line-height: 42rpx">编辑</text> -->
              </view>
              <view class="flex-s" v-else>
                <view class="flex-a-c"><text style="float: left">请选择</text></view>
                <view class="time-icon">
                  <u-icon v-if="!isDisable" name="arrow-right" color="#c7c7c7" size="30"></u-icon>
                </view>
              </view>
            </view>
          </view>
          <view
            v-if="
              (comfireReviewData.week != undefined && comfireReviewData.week.length > 0 && comfireReviewData.startTime != undefined && comfireReviewData.startTime != '') ||
              isDisable
            "
          >
            <view class="mt-30 mb-25" v-for="(item, index) in comfireReviewData.week" :key="index">
              <text class="text-view">{{ getWeekName(item) }}</text>
              <text style="margin-left: 60rpx" class="text-view">{{ comfireReviewData.startTime }}</text>
            </view>
          </view>
        </view>
        <view class="borderB pt-30 pb-30" v-if="!isDisable || firstStudyTime">
          <view class="information">
            <view style="width: 40%">
              <view class="f-30 bold">首次上课时间：</view>
              <!-- <view class="c-99 f-24">限制24小时之后</view> -->
            </view>
            <view v-if="!isDisable || isEdit" class="c-66 f-30" style="width: 60%">
              {{ firstStudyTime ? firstStudyTime : '请先填写上课时间' }}
            </view>
            <view v-else style="width: 60%" class="c-00 f-30">{{ firstStudyTime }}</view>
          </view>
        </view>
        <view class="borderB pt-30 pb-30">
          <view class="flex-a-c f-30">
            <view class="bold" style="width: 63%">是否试课：</view>
            <view v-if="!isDisable || isEdit" class="w100 flex-a-c">
              <view class="flex-a-c mr-30">
                <uni-icons :type="yesExp == 0 ? 'circle' : 'circle-filled'" :color="yesExp == 0 ? '#999' : '#2e896f'" size="22" @click="changeYesExp(1)"></uni-icons>
                <span class="ml-15 c-66">是</span>
              </view>

              <view class="flex-a-c ml-40">
                <uni-icons :type="noExp == 0 ? 'circle' : 'circle-filled'" :color="noExp == 0 ? '#999' : '#2e896f'" size="22" @click="changeNoExp(0)"></uni-icons>
                <span class="ml-15 c-66">否</span>
              </view>
            </view>
            <view style="width: 96%" v-else>{{ infolist.isExp == 0 ? '否' : '是' }}</view>
          </view>
        </view>

        <view v-if="(yesExp == 1 || infolist.isExp) && infolist.curriculumName == '鼎英语'">
          <view class="information ptb-30 borderB">
            <view style="width: 40%" class="f-30 bold">词汇量检测：</view>
            <view style="width: 60%; color: #666666">{{ infolist.wordBase }}</view>
          </view>
        </view>

        <view class="flex-a-c borderB ptb-30 f-30">
          <view class="bold" style="width: 63%">是否新生：</view>
          <view v-if="!isDisable || isEdit" class="w100 flex-a-c">
            <view class="flex-a-c mr-30">
              <uni-icons :type="yesNew == 0 ? 'circle' : 'circle-filled'" :color="yesNew == 0 ? '#999' : '#2e896f'" size="22" @click="changeYesNew(1)"></uni-icons>
              <span class="ml-15 c-66">是</span>
            </view>

            <view class="flex-a-c ml-40">
              <uni-icons :type="noNew == 0 ? 'circle' : 'circle-filled'" :color="noNew == 0 ? '#999' : '#2e896f'" size="22" @click="changeNoNew(0)"></uni-icons>
              <span class="f">否</span>
            </view>
          </view>
          <view style="width: 96%" v-else>{{ infolist.isNewStudent == 0 ? '否' : '是' }}</view>
        </view>
        <view class="ptb-30" style="padding-bottom: 60rpx">
          <view class="f-30 bold">备注：</view>
          <view class="mt-30 p-30 bg-f7 radius-15" v-if="!isDisable || isEdit">
            <textarea @input="inputRemark" maxlength="200" v-model="remark" placeholder-style="color:#999" placeholder="请输入" :disabled="isDisable && !isEdit" />
          </view>

          <view class="mt-30 remake-bg-view" v-else>
            {{ infolist.remark ? infolist.remark : '' }}
          </view>
        </view>
        <!-- </form> -->
        <view class="tips" :style="{ height: svHeight + 'px' }" v-if="orderStatus.type == 3 && orderType == 0">
          <button class="phone-btn" @click="receive">接单</button>
          <button class="phone-btn phone-btn1" @click="nextOrder">下一单</button>
        </view>
      </block>
    </view>
  </view>
</template>

<script>
  // import Util, { objForEach } from '@/util/util.js';
  // const { httpUser } = require('@/util/luch-request/indexUser.js');
  import { Debounce } from '@/utils/debounce.js';
  import dayjs from 'dayjs';
  import Config from '@/utils/config.js';
  export default {
    data() {
      return {
        svHeight: 50,
        useHeight: 0, //除头部之外高度
        codeShow: false,
        qrCode: '',
        infolist: {}, // 回显信息
        orderId: '',
        payStatus: '1', //0填写   1查看
        isDisable: true, //是否禁用

        //是否试过课
        noExp: 0,
        yesExp: 0,
        isExp: '',
        //新生
        yesNew: 0,
        noNew: 0,
        isNewStudent: '',
        remark: '', // 体验需求

        flag: false, // 防止重复点击
        disabled: false,

        imgHost: getApp().globalData.imgsomeHost,

        localWordBase: -1,

        showDialog: false,
        normalWeekData: '周一_周二_周三_周四_周五_周六_周日'.split('_'),

        ///复习
        comfireReviewData: {},
        reviewChoseWeek: [], //复习选择的星期
        rewviewdatevalue: '',
        showReviewTime: false,

        //上课
        comfireCourseData: [],
        courseChoseWeek: [], //上课选择的星期 [{week:"周一", time:[{startTime:"",endTime:""}]},...]
        showCourseTime: false,

        curChoseItem: null,
        curChoseTimeIndex: null,
        curChoseStartFlag: false,

        //课程规划
        courseChoseList: [],

        gradeNameArr: '一年级_二年级_三年级_四年级_五年级_六年级_初一_初二_初三_高一_高二_高三_大一_大二_大三_大四_其他_幼儿园'.split('_'),

        isEdit: false,
        changeRemark: false,
        changeNewStudent: false,
        changeExp: false,
        changeReview: false,
        changeStudy: false,

        firstStudyTime: '',
        firstWeek: '',
        firstTime: '',
        payMent: false,
        orderObj: {},
        orderType: 0,
        deliverMerchant: '',
        orderStatus: {},
        img1: 'https://document.dxznjy.com/automation/1721727506000',
        img2: 'https://document.dxznjy.com/automation/1721727522000',
        navBarTitle: '正式课',
        app: 0,
        CurriculumCodeArr: Config.CurriculumCodeArr,
        isProcessingBack: false,
        needBackList: 1 // 1需要返回接单列表 0需要返回接单大厅退出小程序
      };
    },
    watch: {
      isNewStudent(newVal, oldVal) {
        console.log('isNewStudent修改');
        if (this.isEdit && this.firstStudyTime) {
          this.changeNewStudent = this.isNewStudent != this.infolist.isNewStudent;
          this.setChangeFirstTime();
        }
      },

      isExp(newVal, oldVal) {
        console.log('isExp修改');
        if (this.isEdit && this.firstStudyTime) {
          this.changeExp = this.isExp != this.infolist.isExp;
          this.setChangeFirstTime();
        }
      },

      remark(newVal, oldVal) {
        console.log('remark修改');
        if (this.isEdit && this.firstStudyTime) {
          this.changeRemark = this.remark != this.infolist.remark;
          this.setChangeFirstTime();
        }
      }
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h - 65;
        }
      });
      // 微信小程序需要用此写法
      // #ifdef MP-WEIXIN
      this.$refs.reviewtimePicker.setFormatter(this.formatter);
      this.$refs.coursetimePicker.setFormatter(this.formatter);
      // #endif
    },
    onLoad(e) {
      console.log(e);
      if (e.token) {
        uni.setStorageSync('token', e.token);
      }
      // #ifdef APP-PLUS
      this.app = e.app ? e.app : '';
      this.needBackList = e.needBackList ? e.needBackList : this.needBackList;
      // #endif
      this.orderId = e.id ? e.id : e.orderId ? e.orderId : '';
      if (e.orderType) {
        this.orderType = e.orderType;
      }
      if (e.deliverMerchant) {
        this.deliverMerchant = e.deliverMerchant;
      }
      // this.getCourseinfo();
      // this.getStatus();
      if (this.deliverMerchant && this.orderType == 0) {
        this.getStatus();
      } else {
        this.orderStatus.type = 3;
        this.getCourseinfo();
      }
    },
    onShow() {
      let that = this;
      uni.$on('coursePlan', function (data) {
        that.courseChoseList = data;
        console.log('22222222222222');
        console.log(that.courseChoseList);
        console.log('22222222222222');
      });
      // that.getCourseinfo();
      // that.getStatus();
      if (that.deliverMerchant && this.orderType == 0) {
        that.getStatus();
      } else {
        that.getCourseinfo();
      }
    },
    //  返回接单列表或者接单大厅
    onBackPress() {
      if (this.isProcessingBack) {
        return true; // 阻止默认返回行为
      }
      this.isProcessingBack = true;
      // 获取系统信息判断平台
      const systemInfo = uni.getSystemInfoSync();
      console.log('当前平台:', systemInfo.platform);

      if (systemInfo.platform === 'android') {
        if (this.needBackList == 1) {
          // 需要返回接单列表
          uni.redirectTo({
            url: '/qyWechat/takingOrder'
          });
        } else {
          // 需要返回接单大厅
          plus.runtime.quit(); // 调用退出应用
        }
      } else if (systemInfo.platform === 'ios') {
        plus.runtime.quit(); // 调用退出应用
      }
      return true; // 必须返回 true 才能阻止默认返回
    },
    //  返回接单列表或者接单大厅
    onUnload() {
      if (!this.isProcessingBack) {
        // 正常卸载逻辑
        const systemInfo = uni.getSystemInfoSync();
        if (systemInfo.platform === 'android') {
          return true;
          // uni.redirectTo({
          //   url: '/qyWechat/takingOrder'
          // });
        } else if (systemInfo.platform === 'ios') {
          plus.runtime.quit(); // 调用退出应用
        }
      }
    },

    methods: {
      // 手机号脱敏('13912345678' 转换成 '139****5678') 第3位开始替换4个
      telHide(value) {
        if (!value) {
          return '';
        } else {
          let data = value.replace(/(\d{3})\d{4}(\d*)/, '$1****$2');
          return data;
        }
      },
      // TODO 返回接单列表或者接单大厅
      goBack() {
        if (this.needBackList == 1) {
          // 需要返回接单列表
          uni.redirectTo({
            url: '/qyWechat/takingOrder'
          });
        } else {
          // 需要返回接单大厅
          plus.runtime.quit(); // 调用退出应用
        }
        // // #ifdef APP-PLUS
        // plus.runtime.quit();
        // // #endif
        // if (this.orderType == 2) {
        //   uni.navigateBack();
        // } else {
        //   uni.redirectTo({
        //     url: '/qyWechat/takingOrder'
        //   });
        // }
      },
      getStatus() {
        let that = this;
        console.log({
          id: that.orderId,
          type: 2,
          deliverMerchant: that.deliverMerchant
        });
        that.$http
          .get('/deliver/web/experience/orderMessageInfo', {
            id: that.orderId,
            type: 2,
            deliverMerchant: that.deliverMerchant
          })
          .then(({ data }) => {
            console.log(data, '===============');

            that.orderStatus = data.data;
            if (that.orderStatus.type != 3) {
              that.navBarTitle = ' ';
            } else {
              that.getCourseinfo();
            }
          });
      },
      receive: Debounce(function () {
        let that = this;
        let mobile = uni.getStorageSync('tel');
        // 接单
        // uni.showLoading({
        //   title: '正在接单'
        // });
        that.$http
          .post('/deliver/web/learnManager/receiving', {
            id: that.orderId,
            mobile: mobile
          })
          .then(({ data }) => {
            console.log(data, '=====================');
            if (data.success) {
              // let obj = data.data.data;
              // that.orderObj = data.data.data;
              uni.showToast({
                title: '接单成功'
              });
              // uni.hideLoading();
              // obj.type = 2;
              // let item = encodeURIComponent(JSON.stringify(obj));
              setTimeout(() => {
                plus.runtime.quit();
              }, 500);
            }
          })
          .catch((err) => {
            console.log(err);
            uni.hideLoading();
          });
      }, 500),
      nextOrder: Debounce(function () {
        let that = this;
        // 下一单
        // uni.showLoading({
        //   title: '加载中'
        // });
        that.$http
          .get('/deliver/web/experience/nextExperience', {
            id: that.orderId,
            type: 2
          })
          .then(({ data }) => {
            console.log(data);
            if (data.success) {
              if (data.data.type == 2) {
                that.orderId = data.data.id;
                that.getCourseinfo();
                // uni.hideLoading();
              } else {
                // uni.hideLoading();
                uni.navigateTo({
                  url: `/qyWechat/testClass?orderId=${data.data.id}`
                });
              }
            } else {
              // uni.hideLoading();
              uni.showToast({
                title: data.message,
                icon: 'none'
              });
            }
          })
          .catch((err) => {
            uni.showToast({
              title: '加载错误',
              icon: 'none'
            });
            console.log(err);
            // uni.hideLoading();
          });
      }, 500),
      // 创建群聊
      creteGroup(userId, externalUserId, chatName) {
        let that = this;
        // console.log(userId, externalUserId, chatName);
        wx.qy.openEnterpriseChat({
          // 注意：userIds和externalUserIds至少选填一个，且userIds+externalUserIds总数不能超过2000，如果externalUserIds有微信联系人，则总数不能超过40人。
          //参与会话的企业成员列表，格式为userid1;userid2;...，用分号隔开。
          userIds: userId + ';',
          // 参与会话的外部联系人列表，格式为userId1;userId2;…，用分号隔开。
          // externalUserIds: 'wm6t28KgAAWM25G-r9nX1Dwe-Un_LNxg;wm6t28KgAAKLHeTRKwKUaDAyusyLvfnw;wm6t28KgAASJJmvPheXaRfVROch0G9eQ;',
          externalUserIds: externalUserId + ';',
          groupName: chatName, // 必填，会话名称。单聊时该参数传入空字符串""即可。
          chatId: '', //新建会话时，chatId必须为空串
          success: (res) => {
            // 回调
            var chatId = res.chatId; //返回chatId仅在企业微信3.0.36及以后版本支持；
            console.log(chatId, '1111111111111111111111111');
            that.orderObj.chatId = res.chatId;
            that.$http.post('/deliver/web/learnManager/saveGroup', that.orderObj).then((success) => {
              console.log(success);
            });
          },
          fail: (err) => {
            // 失败处理
            console.log(err, '222222222222222222222222');
          }
        });
      },
      setChangeFirstTime() {
        console.log('修改');
        let isneed = this.changeExp || this.changeNewStudent || this.changeRemark || this.changeReview || this.changeStudy;
        if (isneed) {
          this.getFirstStudyTime();
        } else {
          if (this.isEdit && this.firstStudyTime) {
            this.getFormatToService(this.infolist.firstTime, this.infolist.firstWeek);
          } else {
            this.firstTime = '';
            this.firstStudyTime = '';
            this.firstWeek = '';
          }
        }
      },
      formatter(type, value) {
        if (type === 'hour') {
          return `${value}时`;
        }
        if (type === 'minute') {
          return `${value}分`;
        }
        return value;
      },

      cancel() {
        this.showReviewTime = false;
        this.showCourseTime = false;
      },
      ////////Start////
      // 复习开始时间选择
      reviewDateChange: function (e) {
        this.showReviewTime = false;
        this.$forceUpdate();
      },
      //复习时间--开始时间
      reviewChoseWeekFunc(item, index) {
        let weelIndex = this.reviewChoseWeek.indexOf(index);
        if (weelIndex == -1) {
          this.reviewChoseWeek.push(index);
        } else {
          this.reviewChoseWeek.splice(weelIndex, 1);
        }
      },

      //显示时间
      reviewStartTimeDialog() {
        this.showReviewTime = true;
      },

      reviewConfirm() {
        if (this.reviewChoseWeek.length == 0 && this.rewviewdatevalue.length > 0) {
          uni.showToast({
            icon: 'none',
            title: '请选择星期'
          });
          return;
        }
        if (this.reviewChoseWeek.length > 0 && this.rewviewdatevalue.length <= 0) {
          uni.showToast({
            icon: 'none',
            title: '请选择开始时间'
          });
          return;
        }
        this.reviewChoseWeek.sort((a, b) => a - b);
        this.comfireReviewData = {
          week: this.reviewChoseWeek,
          startTime: this.rewviewdatevalue
        };
        this.$refs.reviewShow.close();
        this.showDialog = false;

        if (this.isEdit && this.firstStudyTime) {
          this.changeReview = true;
          this.setChangeFirstTime();
        }
      },
      openReviewTime() {
        this.$refs.reviewShow.open();
        this.showDialog = true;
      },
      cancelAtion() {
        this.$refs.reviewShow.close();
        this.showDialog = false;
      },
      ///复习时间/////End//////

      /////开始时间////START//////////////
      openTime() {
        this.$refs.courseShow.open();
        this.showDialog = true;
      },
      courseConfirm() {
        let copyArr = this.courseChoseWeek.slice();
        let arr = this.confirmJudgeChoseArr(copyArr);
        if (arr == null) {
          uni.showToast({
            icon: 'none',
            title: '选择的星期下至少需要有一个有效时间段'
          });
          return;
        }
        this.comfireCourseData = arr;
        this.$refs.courseShow.close();
        this.showDialog = false;
        if (!this.isDisable || (this.isEdit && this.firstStudyTime)) {
          this.changeStudy = true;
          this.setChangeFirstTime();
        }
      },
      cancelCourse() {
        this.$refs.courseShow.close();
        this.showDialog = false;
      },
      ChoseWeekFunc(item, index) {
        let weelIndex = this.getWeekForChoseIndex(index);
        if (weelIndex == -1) {
          let insertIndex = -1;
          for (let i = 0; i < this.courseChoseWeek.length; i++) {
            if (index > this.courseChoseWeek[i].weekIndex) {
              continue;
            }
            insertIndex = i;
            break;
          }
          let data = {
            weekIndex: index,
            week: index,
            time: [
              {
                hour: 1,
                startTime: '开始时间',
                endTime: '结束时间'
              }
            ]
          };
          if (insertIndex !== -1) {
            this.courseChoseWeek.splice(insertIndex, 0, data);
          } else {
            this.courseChoseWeek.push(data);
          }
        } else {
          this.courseChoseWeek.splice(weelIndex, 1);
        }
        console.log(this.courseChoseWeek);
        console.log(this.comfireCourseData);
      },

      getWeekForChoseIndex(item) {
        for (let i = 0; i < this.courseChoseWeek.length; i++) {
          let data = this.courseChoseWeek[i];
          if (data.week == item) {
            return i;
          }
        }
        return -1;
      },

      courseSub(item, index) {
        item.time.splice(index, 1);
      },

      courseAdd(item) {
        let data = {
          hour: 1,
          startTime: '开始时间',
          endTime: '结束时间'
        };
        item.time.push(data);
      },

      bindChange(e, item) {
        if (item.startTime == '开始时间') {
          return;
        }
        item.endTime = this.getAutoEndTime(item.startTime, item.hour);
        if (item.startTime != '开始时间' && item.endTime != '结束时间') {
          if (this.judgeAllBetween(item.startTime, item.endTime)) {
            item.endTime = '结束时间';
            uni.showToast({
              icon: 'none',
              title: '选择时间段与其他时间段重叠'
            });
          }
        }
      },

      //自动获取结束时间
      getAutoEndTime(startTime, addHour) {
        let timeParts = startTime.split(':');
        let hours = parseInt(timeParts[0], 10);
        let minutes = parseInt(timeParts[1], 10);

        hours += addHour;
        if (hours >= 24) {
          hours -= 24;
        }
        let endTime = `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}`;
        return endTime;
      },
      showChoseCourseTimeEnd(item) {
        if (item.startTime == '开始时间') {
          uni.showToast({
            icon: 'none',
            title: '请选择开始时间'
          });
        }
      },
      showChoseCourseTime(item, index, isStart) {
        this.showCourseTime = true;

        this.curChoseItem = item;
        this.curChoseTimeIndex = index;
        this.curChoseStartFlag = isStart;
      },
      //上课 开始/结束 时间选择
      courseDateChange: function (e) {
        if (this.judgeStartBetween(e.value)) {
          //判断是否在别的时间内 或者不等于1小时或者2小时
          uni.showToast({
            icon: 'none',
            title: '选择时间与其他时间段重叠'
          });
          return;
        }
        let oldVal = '';
        if (this.curChoseStartFlag) {
          oldVal = this.curChoseItem.time[this.curChoseTimeIndex].startTime;
          this.curChoseItem.time[this.curChoseTimeIndex].startTime = e.value;
          this.curChoseItem.time[this.curChoseTimeIndex].endTime = this.getAutoEndTime(e.value, this.curChoseItem.time[this.curChoseTimeIndex].hour);
        }
        // else{
        //     oldVal = this.curChoseItem.time[this.curChoseTimeIndex].endTime
        //     this.curChoseItem.time[this.curChoseTimeIndex].endTime = e.value
        // }
        let start = this.curChoseItem.time[this.curChoseTimeIndex].startTime;
        let end = this.curChoseItem.time[this.curChoseTimeIndex].endTime;
        if (start != '开始时间' && end != '结束时间') {
          if (this.judgeAllBetween(start, end)) {
            if (this.curChoseStartFlag) {
              if (oldVal != '') {
                this.curChoseItem.time[this.curChoseTimeIndex].startTime = oldVal;
                this.curChoseItem.time[this.curChoseTimeIndex].endTime = this.getAutoEndTime(oldVal, this.curChoseItem.time[this.curChoseTimeIndex].hour);
              } else {
                this.curChoseItem.time[this.curChoseTimeIndex].startTime = '开始时间';
                this.curChoseItem.time[this.curChoseTimeIndex].endTime = '结束时间';
              }
            }
            // else{
            //     if(oldVal != ""){
            //         this.curChoseItem.time[this.curChoseTimeIndex].endTime = oldVal
            //     }else{
            //         this.curChoseItem.time[this.curChoseTimeIndex].endTime = "结束时间"
            //     }
            // }
            uni.showToast({
              icon: 'none',
              title: '选择时间段与其他时间段重叠'
            });
            return;
          }
          // if(this.judgeHours(start,end)){
          //     if(this.curChoseStartFlag){
          //         if(oldVal != ""){
          //             this.curChoseItem.time[this.curChoseTimeIndex].startTime = oldVal
          //             this.curChoseItem.time[this.curChoseTimeIndex].endTime = this.getAutoEndTime(oldVal,this.curChoseItem.time[this.curChoseTimeIndex].hour);
          //         }else{
          //             this.curChoseItem.time[this.curChoseTimeIndex].startTime = "开始时间"
          //             this.curChoseItem.time[this.curChoseTimeIndex].endTime = "结束时间"
          //         }
          //     }
          //     // else{
          //     //     if(oldVal != ""){
          //     //         this.curChoseItem.time[this.curChoseTimeIndex].endTime = oldVal
          //     //     }else{
          //     //         this.curChoseItem.time[this.curChoseTimeIndex].endTime = "结束时间"
          //     //     }
          //     // }
          //     uni.showToast({
          //          icon: "none",
          //         title:"每次上课时间仅可以选择一小时或两小时"
          //     })
          //     return
          // }
        }
        this.showCourseTime = false;
      },
      //开始时间是否在区间内
      judgeStartBetween(start) {
        let targetMinutes = this.timeToMinutes(start);
        for (let i = 0; i < this.curChoseItem.time.length; i++) {
          if (this.curChoseTimeIndex == i) {
            continue;
          }
          let startMinutes = this.timeToMinutes(this.curChoseItem.time[i].startTime);
          let endMinutes = this.timeToMinutes(this.curChoseItem.time[i].endTime);

          let isBetween = targetMinutes >= startMinutes && targetMinutes <= endMinutes;
          if (isBetween) {
            return true;
          }
        }
        return false;
      },
      //整个时间段是否重叠
      judgeAllBetween(start, end) {
        let s1 = this.timeToMinutes(start);
        let e1 = this.timeToMinutes(end);
        for (let i = 0; i < this.curChoseItem.time.length; i++) {
          if (this.curChoseTimeIndex == i) {
            continue;
          }
          let s2 = this.timeToMinutes(this.curChoseItem.time[i].startTime);
          let e2 = this.timeToMinutes(this.curChoseItem.time[i].endTime);

          if (s1 <= s2 && e1 >= e2) {
            return true;
          }
          if (s1 >= s2 && e1 <= e2) {
            return true;
          }
          if (s1 <= s2 && e1 <= e2 && s2 <= e1) {
            return true;
          }
          if (s1 >= s2 && e1 >= e2 && s1 <= e2) {
            return true;
          }
        }
        return false;
      },
      //是否是两小时、一小时
      judgeHours(start, end) {
        let startMinutes = this.timeToMinutes(start);
        let endMinutes = this.timeToMinutes(end);
        let timeDifferenceInMinutes = endMinutes - startMinutes;
        console.log(timeDifferenceInMinutes);
        if (timeDifferenceInMinutes != 60 && timeDifferenceInMinutes != 120) {
          return true;
        }
        return false;
      },
      // 将时间字符串转换为分钟数
      timeToMinutes(time) {
        let [hours, minutes] = time.split(':').map(Number);
        return hours * 60 + minutes;
      },
      confirmJudgeChoseArr(arr) {
        for (let i = 0; i < arr.length; i++) {
          let timeArr = arr[i].time;
          for (let j = timeArr.length - 1; j >= 0; j--) {
            if (timeArr.length > 1 && timeArr[j].startTime == '开始时间' && timeArr[j].endTime == '结束时间') {
              timeArr.splice(j, 1);
            } else if (timeArr[j].startTime == '开始时间' || timeArr[j].endTime == '结束时间') {
              return null;
            }
          }
        }
        return arr;
      },
      //时间展示
      getAllTimeShow(time) {
        let timeDetails = '';
        for (let i = 0; i < time.length; i++) {
          timeDetails += time[i].startTime + '~' + time[i].endTime + ' ';
        }
        return timeDetails;
      },
      /////开始时间////END//////////////

      /////课程规划///////////START////////
      openCoursePlan() {
        uni.navigateTo({
          url: '/Recharge/onlineJoinTable/coursePlan?choseList=' + encodeURIComponent(JSON.stringify(this.courseChoseList))
        });
      },
      /////课程规划///////////END////////

      //是否试过课
      changeYesExp(value) {
        this.yesExp = 1;
        this.noExp = 0;
        this.isExp = value;
        if (this.localWordBase == -1) {
          this.getVocabulary();
        }
      },
      changeNoExp(e) {
        this.isExp = e;
        this.yesExp = 0;
        this.noExp = 1;
      },
      //是否新生
      changeYesNew(value) {
        this.yesNew = 1;
        this.noNew = 0;
        this.isNewStudent = value;
      },
      changeNoNew(e) {
        this.yesNew = 0;
        this.noNew = 1;
        this.isNewStudent = e;
      },
      inputRemark(e) {
        this.remark = e.detail.value;
      },

      //修改上课对接信息表
      async editOption() {
        let _this = this;
        uni.showLoading();
        let data = {
          firstTime: _this.firstTime,
          firstWeek: _this.firstWeek,
          id: _this.orderId,
          isExp: _this.isExp,
          isNewStudent: _this.isNewStudent,
          remark: _this.remark,
          reviewTime: _this.comfireReviewData.startTime,
          reviewWeek: JSON.stringify(_this.comfireReviewData.week),
          studyTimeList: _this.getStudyTimeList(),
          wordBase: _this.infolist.wordBase
        };
        try {
          let res = await this.$http.post('deliver/web/student/contact/info/updateStudentContactInfo', data);
          if (res) {
            uni.reLaunch({
              //redirectTo
              url: '/Recharge/onlineJoinTable/onlineJoinTable'
            });
            _this.flag = false;
            _this.disabled = false;
          } else {
            _this.flag = false;
            _this.disabled = false;
          }
        } catch (e) {
          _this.flag = false;
          _this.disabled = false;
        }
        uni.hideLoading();
      },

      //新增上课对接信息表
      async sendCourse() {
        let _this = this;
        if (_this.flag) {
          return;
        }
        _this.flag = true;
        _this.disabled = true;
        if (_this.courseChoseList.length <= 0) {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请填写课程规划');
        }
        if (_this.comfireCourseData.length <= 0) {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请填写上课时间');
        }
        if (
          _this.comfireReviewData.week == undefined ||
          _this.comfireReviewData.week.length <= 0 ||
          _this.comfireReviewData.startTime == undefined ||
          _this.comfireReviewData.startTime == ''
        ) {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请填写复习时间');
        }
        if (_this.isExp === '') {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请选择是否试过课');
        }
        if (_this.isNewStudent === '') {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请选择是否是新生');
        }
        //修改上课信息对接表
        if (_this.isEdit) {
          _this.editOption();
          return;
        }
        if (!res1.data.data) {
          _this.flag = false;
          _this.disabled = false;
          _this.codeShow = true;
          return;
        }
        // uni.showLoading();
        let data = {
          courseProject: JSON.stringify(_this.courseChoseList),
          id: _this.orderId,
          isExp: _this.isExp,
          isNewStudent: _this.isNewStudent,
          remark: _this.remark,
          reviewTime: _this.comfireReviewData.startTime,
          reviewWeek: JSON.stringify(_this.comfireReviewData.week),
          studyTimeList: _this.getStudyTimeList(),
          wordBase: _this.infolist.wordBase,
          firstTime: _this.firstTime,
          firstWeek: _this.firstWeek
        };
        try {
          let res = await this.$http.post('deliver/web/student/contact/info/submitStudentContactInfo', data);
          // if (res) {
          // 	uni.reLaunch({ //redirectTo
          // 		url: '/Recharge/onlineJoinTable/onlineJoinTable'
          // 	})
          // 	_this.flag = false;
          // 	_this.disabled = false;
          // } else {
          // 	_this.flag = false;
          // 	_this.disabled = false;
          // }
          if (res) {
            if (!res.data) {
              _this.$refs.tipsPopup.open();
              return;
            }
            uni.reLaunch({
              //redirectTo
              url: '/Recharge/onlineJoinTable/onlineJoinTable'
            });
            _this.flag = false;
            _this.disabled = false;
            // if (!res.data) {
            // 	_this.$refs.tipsPopup.open();
            // 	return;
            // } else {
            // 	uni.showToast({
            // 		icon: "none",
            // 		title: "操作成功"
            // 	})
            // 	// uni.navigateBack();
            // 	uni.navigateTo({
            // 		url: '/Recharge/onlineJoinTable/onlineJoinTable'
            // 	})
            // }
          } else {
            uni.hideLoading();
            _this.flag = false;
            _this.disabled = false;
          }
        } catch (e) {
          uni.hideLoading();
          _this.flag = false;
          _this.disabled = false;
        }
        // uni.hideLoading()
      },

      //[{week:"周一", time:[{startTime:"",endTime:""}]},...]
      //=>
      // {"endTime": "string","startTime": "string","usableHourEnd": 0,
      // "usableHourStart": 0,"usableMinuteEnd": 0,"usableMinuteStart": 0,"usableWeek": 0
      getStudyTimeList() {
        let dataArr = [];
        for (let i = 0; i < this.comfireCourseData.length; i++) {
          for (let j = 0; j < this.comfireCourseData[i].time.length; j++) {
            let startTimeArr = this.getSplitHour(this.comfireCourseData[i].time[j].startTime, ':');
            let endTimeArr = this.getSplitHour(this.comfireCourseData[i].time[j].endTime, ':');
            let data = {
              endTime: '',
              startTime: '',
              usableHourEnd: Number(endTimeArr[0]),
              usableMinuteEnd: Number(endTimeArr[1]),
              usableHourStart: Number(startTimeArr[0]),
              usableMinuteStart: Number(startTimeArr[1]),
              usableWeek: this.comfireCourseData[i].week
            };
            dataArr.push(data);
          }
        }
        return dataArr;
      },

      getCourseDataFormServer() {
        let dataArr = [];
        for (let i = 0; i < this.infolist.studyTimeList.length; i++) {
          console.log(this.infolist.studyTimeList[i]);
          let weekIndex = this.getChoseIndex(dataArr, this.infolist.studyTimeList[i].usableWeek);
          if (weekIndex == -1) {
            let date = {
              week: this.infolist.studyTimeList[i].usableWeek,
              time: [
                {
                  startTime: this.infolist.studyTimeList[i].startTime,
                  endTime: this.infolist.studyTimeList[i].endTime,
                  hour: this.getHourForService(this.infolist.studyTimeList[i])
                }
              ]
            };
            dataArr.push(date);
          } else {
            let timeData = {
              startTime: this.infolist.studyTimeList[i].startTime,
              endTime: this.infolist.studyTimeList[i].endTime,
              hour: this.getHourForService(this.infolist.studyTimeList[i])
            };
            dataArr[weekIndex].time.push(timeData);
          }
        }
        console.log(dataArr);
        this.comfireCourseData = dataArr;
        this.courseChoseWeek = dataArr;
      },

      getHourForService(timeData) {
        let timePartstart = timeData.startTime.split(':');
        let startHours = parseInt(timePartstart[0], 10);
        let timePartend = timeData.endTime.split(':');
        let endHours = parseInt(timePartend[0], 10) + 24;
        let diff = endHours - startHours;
        if (diff > 24) {
          return diff - 24;
        } else {
          return diff;
        }
      },

      getChoseIndex(arr, item) {
        for (let i = 0; i < arr.length; i++) {
          let data = arr[i];
          if (data.week == item) {
            return i;
          }
        }
        return -1;
      },

      getSplitHour(time, str) {
        let arr = time.split(str);
        return arr;
      },

      getWeekName(week) {
        return this.normalWeekData[week];
      },

      async getCourseinfo() {
        let _this = this;

        uni.showLoading();
        const res = await this.$http.get('/deliver/web/student/contact/info/getStudentByDeliverId', {
          id: _this.orderId
        });
        // const res = await this.$http({
        //   url: 'deliver/web/student/contact/info/getStudentContactInfoDetail',
        //   method: 'get',
        //   data: {
        //     id: _this.orderId
        //   }
        // });
        uni.hideLoading();
        if (res && res.data) {
          _this.isDisable = res.data.isSubmit;
          _this.infolist = res.data.data;
          console.log(res.data.data, '========');
          if (_this.infolist.isSubmit) {
            _this.getNormalExp(_this.infolist.isExp);
            _this.getNormalNew(_this.infolist.isNewStudent);
            _this.remark = _this.infolist.remark;
            _this.getCourseDataFormServer();
            _this.courseChoseList = _this.infolist.courseProject ? JSON.parse(_this.infolist.courseProject) : '';
            _this.getNormalReviewData(_this.infolist);
            _this.getFormatToService(_this.infolist.firstTime, _this.infolist.firstWeek);
          }
        }
      },

      getFormatToService(date, week) {
        if (date) {
          this.firstTime = date;
          let str = dayjs(date).format('MM月DD日& HH:mm');
          let allStr = str;
          if (week) {
            allStr = str.replace('&', this.getWeekName(week));
            this.firstWeek = week;
          } else {
            allStr = str.replace('&', '');
          }
          this.firstStudyTime = allStr;
        }
      },

      getNormalReviewData(infolist) {
        this.reviewChoseWeek = JSON.parse(infolist.reviewWeek);
        this.rewviewdatevalue = infolist.reviewTime;
        this.comfireReviewData = {
          startTime: this.rewviewdatevalue,
          week: this.reviewChoseWeek
        };
      },

      getNormalExp(val) {
        this.isExp = val;
        this.yesExp = this.isExp ? 1 : 0;
        this.noExp = this.isExp ? 0 : 1;
      },
      getNormalNew(val) {
        this.isNewStudent = val;
        this.yesNew = this.isNewStudent ? 1 : 0;
        this.noNew = this.isNewStudent ? 0 : 1;
      },
      getGrade(val) {
        if (val) {
          val -= 1;
          let index = 0;
          if (val <= 0) {
            index = 0;
          } else if (val >= this.gradeNameArr.length) {
            index = this.gradeNameArr.length - 1;
          } else {
            index = val;
          }
          return this.gradeNameArr[index];
        }
        return this.gradeNameArr[this.gradeNameArr.length - 1];
      },

      //获取词汇
      async getVocabulary() {
        let _this = this;
        uni.showLoading();
        const res = await this.$http.get('znyy/course/sel/last/test', {
          studentCode: _this.infolist.studentCode
        });
        // const res = await this.$http({
        //   url: 'znyy/course/sel/last/test',
        //   method: 'get',
        //   data: {
        //     studentCode: _this.infolist.studentCode
        //   }
        // });
        uni.hideLoading();
        if (res && res.data) {
          _this.localWordBase = res.data.vocabulary;
          _this.infolist.wordBase = res.data.vocabulary;
        }
      },

      //计算首次上课时间
      getFirstStudyTime() {
        console.log('计算');
        if (this.comfireCourseData.length == 0) {
          this.firstWeek = '';
          this.firstTime = '';
          this.firstStudyTime = '';
          return;
        }
        let dateArr = [];
        let dateMinArr = [];
        let nowDate = dayjs().format('YYYY-MM-DD HH:mm');
        for (var i = 0; i < this.comfireCourseData.length; i++) {
          for (let j = 0; j < this.comfireCourseData[i].time.length; j++) {
            let date = this.getChoseDataToOption(this.comfireCourseData[i].week, this.comfireCourseData[i].time[j]);
            dateArr.push(date);
            let dayjs1 = dayjs(date);
            dateMinArr.push(dayjs1.diff(nowDate, 'minute'));
          }
        }
        if (dateArr.length === 0) {
          this.firstWeek = '';
          this.firstTime = '';
          this.abutmentList.firstStudyTime = '';
          return;
        }
        let needIndex = -1;
        let minMinVal = Infinity;
        for (let i = 0; i < dateMinArr.length; i++) {
          if (dateMinArr[i] > 24 * 60 && dateMinArr[i] < minMinVal) {
            needIndex = i;
            minMinVal = dateMinArr[i];
          }
        }
        if (needIndex != -1) {
          this.firstStudyTime = this.getFormatToShow(dateArr[needIndex]);
        } else {
          let minIndex = dateMinArr.indexOf(Math.min(...dateMinArr));
          let lastDate = dayjs(dateArr[minIndex]).add(7, 'day');
          this.firstStudyTime = this.getFormatToShow(lastDate);
        }
      },

      getFormatToShow(date) {
        let str = dayjs(date).format('MM月DD日& HH:mm');
        let weekIndex = this.getLocalTypeWeek(dayjs(date).day());
        this.firstTime = dayjs(date).format('YYYY-MM-DD HH:mm');
        this.firstWeek = weekIndex;
        let allStr = str.replace('&', this.getWeekName(weekIndex));
        return allStr;
      },

      getChoseDataToOption(week, data) {
        let date = this.getDateForWeek(week);
        return date + ' ' + data.startTime;
      },

      getDateForWeek(week) {
        let nowWeek = this.getLocalTypeWeek(dayjs().day());
        let diff = week - nowWeek;
        let date = '';
        if (diff >= 0) {
          date = dayjs().add(Math.abs(diff), 'day').format('YYYY-MM-DD');
        } else {
          date = dayjs().subtract(Math.abs(diff), 'day').format('YYYY-MM-DD');
        }
        return date;
      },

      getLocalTypeWeek(nowWeek) {
        return (nowWeek + 6) % 7;
      },
      skintap(url) {
        $navigationTo(url);
      },
      closeTipsDialog() {
        if (this.payMent) {
          uni.navigateBack({
            //redirectTo
            delta: 2
          });
        } else {
          this.$refs.tipsPopup.close();
          uni.showToast({
            icon: 'none',
            title: '操作成功'
          });
          uni.navigateBack();
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  page {
    background-color: #fff;
  }

  .information {
    display: flex;
    justify-content: space-between;
    align-items: center;

    /deep/.uni-icons {
      color: #fff !important;
    }
  }

  .phone-input {
    background: #fff;
    border-radius: 8rpx;
    width: 100%;
    height: 70rpx;
    font-size: 28rpx;
    color: #999;
    display: flex;
    padding-left: 30rpx;
    align-items: center;
  }

  .uni-list-cell-db {
    background: #fff;
    border-radius: 8rpx;
    width: 100%;
    height: 70rpx;
    font-size: 28rpx;
    color: #999;
    display: flex;
    padding-left: 20rpx;
    align-items: center;
  }

  /deep/.date_color {
    color: #000 !important;
  }

  /deep/.regions {
    color: #999 !important;
    font-size: 30upx;
  }

  .tips {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  /deep/.phone-btn {
    width: 310rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 40rpx;
    font-size: 30rpx;
    color: #fff !important;
    background: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  /deep/.phone-btn1 {
    border: 1rpx solid #1d755c;
    color: #1d755c !important;
    background: #fff;
  }

  /deep/.uni-select {
    padding: 0 10rpx 0 0;
    border: 0;
  }

  /deep/.uni-select__input-placeholder {
    font-size: 28rpx;
  }

  /deep/.uni-select--disabled {
    background-color: #fff;
  }

  /deep/.uni-stat__select {
    height: 60rpx !important;
  }

  .borderB {
    border-bottom: 1px solid #efefef;
  }

  /deep/.uni-select__input-placeholder {
    color: #999 !important;
    font-size: 30rpx !important;
  }

  .icon_x {
    position: absolute;
    top: 28rpx;
    right: 0;
    z-index: 1;
  }

  .time-icon {
    /deep/.u-icon--right {
      position: absolute;
      right: 0;
      top: 20rpx;
    }
  }

  /deep/.u-picker__view {
    height: 600rpx !important;
  }

  .dialogBG {
    margin: 0 20rpx 20rpx 20rpx;
    height: 961rpx;
    background-color: #fff;
    border-radius: 12rpx;
  }

  .top-close {
    color: black;
    font-size: 30rpx;
    margin: 0 35rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .center-content {
    margin-top: 40rpx;
    height: 760rpx;
  }

  .start-time {
    margin: 30rpx 35rpx 0rpx 35rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: black;
  }

  .top-button {
    text-align: center;
    height: 80rpx;
    display: flex;
    justify-content: center;
  }

  .confirm-button {
    width: 250rpx;
    height: 80rpx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    color: #fff;
    font-size: 32rpx;
    display: flex;
    justify-content: center;
    /* 文本水平居中对齐 */
    align-items: center;
    /* 文本垂直居中对齐 */
  }

  .chose-week-text {
    width: 100%;
    margin: 0 15rpx;
    display: flex;
    align-content: flex-start;
    flex-flow: row wrap;
  }

  .chose-week-item {
    margin: 0 20rpx 30rpx 20rpx;
  }

  .week-normal {
    width: 120rpx;
    height: 65rpx;
    background: rgba(177, 177, 177, 0.2);
    border-radius: 12rpx;
    border: 1rpx solid #b1b1b1;
    color: #000000;
    font-size: 30rpx;
    display: flex;
    align-items: center;
    /* 垂直居中 */
    justify-content: center;
    /* 水平居中 */
  }

  .week-chose {
    width: 120rpx;
    height: 65rpx;
    background: rgba(46, 137, 111, 0.1);
    border-radius: 12rpx;
    border: 1rpx solid #2e896f;
    color: #2e896f;
    font-size: 30rpx;
    display: flex;
    align-items: center;
    /* 垂直居中 */
    justify-content: center;
    /* 水平居中 */
  }

  .text-view {
    font-size: 32rpx;
    color: #333333;
  }

  .text-more-view {
    display: flex;
    flex-direction: row;
    /* 设置为水平排列 */
  }

  .course-bg-view {
    width: 530rpx;
    height: 60rpx;
    line-height: 60rpx;
    background: rgba(153, 153, 153, 0.1);
    border-radius: 12rpx;
    // padding-top: 24rpx;
    padding-left: 24rpx;
    padding-right: 24rpx;
    font-size: 26rpx;
    color: #666666;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .remake-bg-view {
    background: rgba(153, 153, 153, 0.1);
    border-radius: 12rpx;
    padding: 24rpx 35rpx;
    font-size: 28rpx;
    color: #000;
  }

  .time-flag {
    width: 38rpx;
    height: 38rpx;
    margin-top: 5rpx;
    margin-left: 20rpx;
  }

  .week-view {
    margin-left: 35rpx;
    margin-right: 24rpx;
    display: flex;
    justify-content: space-between;
  }

  .time-view {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .time-text-view-normal {
    color: #999999;
    width: 116rpx;
    font-size: 28rpx;
  }

  .time-text-view-normal-1 {
    border-radius: 12rpx;
    border: 1px solid #c8c8c8;
    background-color: #f4f4f4;
    color: #000;
    width: 116rpx;
    padding: 10rpx 0;
    font-size: 28rpx;
    transform: scale(0.995);
    /* 解决ios上圆角失效 */
  }

  .time-text-view {
    color: #000000;
    width: 116rpx;
    font-size: 30rpx;
  }

  .thesaurus {
    background-color: #1d755c;
    padding: 5rpx 8rpx;
  }

  .first-class-vocabulary {
    position: absolute;
    top: 10rpx;
    right: 2rpx;
    color: #fff;
    height: 40rpx;
    font-size: 24rpx;
    line-height: 40rpx;
    padding: 0 5rpx;
    background-color: #2f8069;
    border-radius: 10rpx 0 0 10rpx;
  }

  .course {
    width: 420rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 弹窗样式 */
  .dialog_style {
    width: 100%;
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 680rpx;
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 45upx;
    box-sizing: border-box;
  }

  .successFn-img {
    position: absolute;
    left: 160rpx;
    top: -298rpx;
    width: 300rpx;
    height: 300rpx;
    z-index: 9;
  }

  .successFnContent {
    font-size: 30rpx;
    font-family: AlibabaPuHuiTiR;
    color: #333333;
    line-height: 50rpx;
    margin-top: 40rpx;
    margin-bottom: 40rpx;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .review_btn {
    width: 250upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    justify-content: center;
    text-align: center;
  }

  .close_btn {
    width: 250upx;
    height: 80upx;
    color: #2e896f;
    font-size: 30upx;
    line-height: 80upx;
    text-align: center;
    border-radius: 40upx;
    box-sizing: border-box;
    border: 1px solid #2e896f;
    overflow: visible;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 75rpx;
    z-index: -1;
  }

  .orderinfo {
    position: relative;
    width: 500rpx;
    font-size: 30rpx;
    border-radius: 24rpx;
    padding: 50rpx 0;
    background-color: #fff;

    .color {
      color: #7a7a7a;
    }

    .btns {
      display: flex;
      justify-content: space-around;
      margin-top: 20rpx;
    }

    .btn {
      width: 160rpx;
      height: 50rpx;
      border-radius: 50rpx;
      line-height: 50rpx;
      text-align: center;
      font-size: 20rpx;
    }

    .btn1 {
      color: #64a795;
      background-color: #ffffff;
      border: 1px solid #64a795;
    }

    .btn2 {
      background-color: #469880;
      // background-image: linear-gradient(60deg, #64b3f4 0%, #c2e59c 100%);
      color: #fff;
      border: 1px solid transparent;
    }
  }

  .flex-coum {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
</style>
