<template>
  <view class="review-container">
    <view class="top-view">
      <view style="position: relative">
        <uni-icons style="position: absolute; left: 10rpx; top: 100rpx" type="arrowleft" class="backIcon" size="20" color="#000" @click="goback()"></uni-icons>
        <text style="position: absolute; left: 56rpx; top: 98rpx">复习单词</text>
        <text v-if="isShowDown" style="position: absolute; top: 98rpx; left: 50%; transform: translateX(-50%)">
          复习时长剩余：
          <text>{{ countdownStr }}</text>
        </text>
      </view>
    </view>
    <view style="margin-top: 160rpx">
      <view
        class="review clearfix"
        v-for="(item, index) in wordList"
        :key="index"
        @click="sayWord(item.word)"
        :class="{ bgColor: arrIndex.indexOf(index) > -1, 'review-grey': item.isToday == 0 }"
      >
        <text class="fl flHeight" :style="item.word.length < 18 ? 'line-height:90rpx' : ''">{{ item.word }}</text>
        <view class="fr choose">
          <image @click.stop="correctWord(index, item.word, item.scheduleCode, 0)" :src="errorimage(index)"></image>
          <image @click.stop="correctWord(index, item.word, item.scheduleCode, 1)" :src="rightimage(index)"></image>
        </view>
        <view class="fr translation overstepSingle" :class="{ hide: arrIndex.indexOf(index) == -1 }">
          {{ item.translation }}
        </view>
      </view>
    </view>
    <view class="footer clearfix" v-if="footerShow == 0">
      <view class="fl statistics">
        <image src="https://document.dxznjy.com/applet/newimages/duihaogrenn.png"></image>
        <view class="word">{{ rightNum }}词</view>
        <image src="https://document.dxznjy.com/applet/newimages/chahaores.png"></image>
        <view class="word">{{ errorNum }}词</view>
      </view>
      <button class="fr submint" :disabled="isClick" @click="$noMultipleClicks(submintWord)">提交</button>
    </view>
    <view class="zanwu" :status="loadingType"></view>
    <!-- 没有更多数据了~ -->
  </view>
</template>
<script>
  import CryptoJS from 'crypto-js';
  var innerAudioContext;
  export default {
    components: {},
    data() {
      return {
        noClick: true, //防抖
        arrIndex: [], //当前行背景色和汉字显示 标记
        errorIndex: [], //错误标记
        rightIndex: [], //正确标记
        wordCount: 0, //单词总数
        loadingType: 'more', //加载更多状态
        wordList: [], //单词集合
        pageindex: 1, //当前页
        pageSize: 20, //页数
        arrWord: [], //当前数据集合
        errorNum: 0, //错题数量
        rightNum: 0, //对题数量
        studentCode: '', //学员Code
        isplay: false,
        footerShow: 0,
        isClick: false, //提交是否可以点击

        countdownStr: '00:00',
        countdown: 0, // 设定初始倒计时时间，单位为秒
        timer: null, // 存储计时器ID

        deliverMerchant: '',
        merchantCode: '',
        isShowDown: false,
        userlist: '',

        timbre: 'W', // 音色默认女声 M  W
        pronunciationType: 0, // 1英式  0美式  默认美式
        playType: 2, // 版本
        linkUrl: '',
        userCode: '',
        sg: ''
      };
    },
    onShow() {
      uni.hideToast();
      this.getPersonalList();
      this.getWordversion();
    },
    methods: {
      async getPersonalList() {
        let { data } = await uni.$http.get('/deliver/app/teacher/info');
        this.userlist = data.data;
        let list = this.userlist.teacherCode + 'L0anhf';
        this.sg = CryptoJS.SHA1(list).toString();
        console.log('加密结果：', this.sg);
      },
      // 获取当前学员设置的语音版本
      getWordversion() {
        var that = this;
        uni.$http
          .get('/znyy/course/info', {
            studentCode: that.studentCode
          })
          .then((res) => {
            if (res.data.success) {
              console.log(res.data.data);
              if (res.data.data.voiceModel) {
                let name = res.data.data.voiceModel.split('#');
                that.pronunciationType = name[1];
                that.timbre = name[2];
                that.playType = 1;
                // #ifdef MP-WEIXIN
                that.playType = name[0];
                // #endif
              }
            } else {
              that.$util.alter(res.data.message);
            }
          });
      },
      goback() {
        uni.navigateBack();
      },
      errorimage(index) {
        // return this.errorIndex.indexOf(index) > -1 ? '../../static/error01.png' : '../../static/error.png';
        return this.errorIndex.indexOf(index) > -1 ? 'https://document.dxznjy.com/applet/newimages/chahaores.png' : 'https://document.dxznjy.com/applet/newimages/chahao.png';
      },
      rightimage(index) {
        // return this.rightIndex.indexOf(index) > -1 ? '../../static/correct01.png' : '../../static/correct.png';
        return this.rightIndex.indexOf(index) > -1 ? 'https://document.dxznjy.com/applet/newimages/duihaogrenn.png' : 'https://document.dxznjy.com/applet/newimages/duhao.png';
      },
      sayWord(word) {
        console.log('-----------------........................----------------------------');
        var that = this;
        // console.log(word);
        uni.$http
          .get('/znyy/app/query/word/voice', {
            word: word,
            v: that.playType,
            rp: that.pronunciationType == 1 ? true : false,
            sex: that.timbre,
            sg: that.sg
          })
          .then((result) => {
            if (result.data.success) {
              let voiceUrl;
              let url;
              if (that.playType == 1) {
                voiceUrl = 'https://document.dxznjy.com/' + encodeURIComponent(result.data.data);
                that.linkUrl = voiceUrl;
              } else {
                voiceUrl = result.data.data;
                that.linkUrl = voiceUrl;
              }
              // #ifdef MP-WEIXIN
              innerAudioContext.obeyMuteSwitch = false;
              // #endif
              console.log(that.linkUrl);
              innerAudioContext.src = that.linkUrl;
              innerAudioContext.play();
              // var w = word.substring(0, 1);
              // var w = encodeURIComponent(result.data.data);
              // var linkUrl = 'https://document.dxznjy.com/' + w + '.mp3';
              // innerAudioContext.obeyMuteSwitch = false;
              // // console.log(linkUrl)
              // innerAudioContext.src = linkUrl;
              // innerAudioContext.play();
            } else {
              that.$util.alter(result.data.message);
            }
          });
      },
      async loadWordList(type = 'add', loading) {
        console.log('11111111');
        var that = this;
        if (type === 'add') {
          if (this.loadingType === 'nomore') {
            console.log('this.loadingType === nomore');
            return;
          }
          this.loadingType = 'loading';
        } else {
          this.loadingType = 'more';
          console.log('this.loadingType === more');
        }

        var mindex = this.pageindex;
        let wordCountResult = await uni.$http.get('/znyy/review/query/fun/word/count', {
          studentCode: that.studentCode
        });
        if (wordCountResult) {
          if (wordCountResult.data.data != null) {
            console.log(wordCountResult.data.data);
            that.wordCount = wordCountResult.data.data;
          }
        }
        console.log(that.wordCount, 'wordwordword');
        let result = await uni.$http.get('/znyy/review/query/fun/word/' + mindex + '/' + this.pageSize + '/' + this.studentCode);
        if (type === 'refresh') {
          that.wordList = [];
        }
        if (result) {
          if (result.data.data.data.length == 0) {
            // if (mindex <= 1 && result.data.data.length == 0) {
            // that.wordList = [];
            that.loadingType = 'nodata';
            that.footerShow = 1;
          } else {
            if (result.data.data.data.length > 0) {
              that.wordList = that.wordList.concat(result.data.data.data);
            }
            // console.log(that.pageindex,Number(result.data.data.totalPage))
            that.loadingType = that.pageindex >= Number(result.data.data.totalPage) ? 'nomore' : 'more';
          }
          if (type === 'refresh') {
            if (loading == 1) {
              uni.hideLoading();
            } else {
              uni.stopPullDownRefresh();
            }
          }
        }
      },
      // togglePopup: function() {
      // 	this.$nextTick(() => {
      // 		this.$refs['showtip'].open();
      // 	})
      // },
      // cancelPopup() {
      // 	this.$refs['showtip'].close();
      // },
      correctWord(index, word, scheduleCode, correct) {
        //单词、标记、对错 0是错 1是对
        if (this.arrIndex != null && this.arrIndex.indexOf(index) == -1) {
          this.arrIndex.push(index);
        }
        if (correct == 0) {
          if (this.errorIndex != null && this.errorIndex.indexOf(index) == -1) {
            this.errorIndex.push(index);
          }
          if (this.rightIndex.indexOf(index) > -1) this.rightIndex.splice(this.rightIndex.indexOf(index), 1);
        }
        if (correct == 1) {
          if (this.rightIndex != null && this.rightIndex.indexOf(index) == -1) {
            this.rightIndex.push(index);
          }
          if (this.errorIndex.indexOf(index) > -1) this.errorIndex.splice(this.errorIndex.indexOf(index), 1);
        }
        var that = this;
        var arrWord = that.arrWord;
        if (arrWord.length == 0) {
          console.log(arrWord);
          var model = {
            word: word,
            scheduleCode: scheduleCode,
            correct: correct,
            translation: that.wordList[index].translation
          };
          arrWord.push(model);
          console.log(2222222222222);
          if (correct == 0) that.errorNum += 1;
          else that.rightNum += 1;
        } else {
          var i = 0;
          console.log(word);
          console.log(scheduleCode);
          console.log(arrWord);
          arrWord.forEach(function (item) {
            if (item.word == word && item.scheduleCode == scheduleCode) {
              i = 1;
              console.log(333333333333333);
              if (item.correct != correct) {
                if (item.correct == 0) {
                  that.errorNum -= 1;
                  that.rightNum += 1;
                } else {
                  that.errorNum += 1;
                  that.rightNum -= 1;
                }
              }
              item.correct = correct;
              return;
            }
          });
          if (i == 0) {
            var model = {
              word: word,
              scheduleCode: scheduleCode,
              correct: correct,
              translation: that.wordList[index].translation
            };
            arrWord.push(model);
            console.log(1111111111);
            if (correct == 0) that.errorNum += 1;
            else that.rightNum += 1;
          }
        }
        that.arrWord = arrWord;
      },
      submintWord() {
        // console.log(JSON.stringify(this.arrWord));
        var that = this;
        var worded = parseInt(that.errorNum) + parseInt(that.rightNum); //已经复习单词总数
        if (worded <= 0) {
          this.$util.alter('未复习，不能提交！');
        } else {
          // that.isClick = true;
          uni.showLoading();
          var rate = Math.round((parseInt(that.rightNum) / worded) * 10000) / 100.0;
          uni.$http
            .post('/znyy/review/fun/review/mark/word', {
              studentCode: that.studentCode,
              // reviewWordVM: JSON.stringify(that.arrWord),
              reviewWordVM: that.arrWord,
              rate: rate,
              totalWordCount: that.wordCount.totalWordCount,
              merchantCode: that.merchantCode
            })
            .then((result) => {
              if (!result.data.success) {
                that.$util.alter(result.data.message);
              } else {
                uni.hideLoading();
                // this.$util.alter('提交成功');
                // uni.redirectTo({
                // 	url: `/antiForgetting/report?wordcount=${that.wordCount}&worded=${worded}&rate=${rate}&studentCode=${that.studentCode}`
                // });
                let data1 = {
                  studentCode: that.studentCode,
                  reviewWordVM: that.arrWord,
                  rate: rate,
                  wordCount: that.wordCount.totalWordCount,
                  studentCode: that.studentCode,
                  reviewId: result.data.data
                };
                uni.redirectTo({
                  url: '/antiForgetting/reviewReport?data=' + encodeURIComponent(JSON.stringify(data1))
                });
              }
            });
        }
      },

      getReviewTime() {
        uni.showLoading();
        uni.$http
          .get(
            '/deliver/web/learnManager/getStudentHaveReviewMinute?studentCode=' +
              this.studentCode +
              '&merchantCode=' +
              this.merchantCode +
              '&deliverMerchant=' +
              this.deliverMerchant
          )
          .then((result) => {
            uni.hideLoading();
            if (!result.data.success) {
              this.$util.alter(result.data.message);
              return;
            }
            console.log(result);
            if (result.data.data) {
              this.countdown = Number(result.data.data) * 60;
              this.isShowDown = this.countdown <= 10 * 60;
              this.countdownStr = this.getCountdownStr();
              if (this.countdown > 0) {
                this.startCountdown();
              }
            }
          });
      },
      getCountdownStr() {
        let min = parseInt(this.countdown / 60);
        let sec = parseInt(this.countdown % 60);
        return (min < 10 ? '0' + min : min) + ':' + (sec < 10 ? '0' + sec : sec);
      },
      startCountdown() {
        this.timer = setInterval(() => {
          // 每秒减少倒计时时间
          this.countdown--;
          this.countdownStr = this.getCountdownStr();
          // 倒计时结束，清除计时器
          if (this.countdown <= 0) {
            clearInterval(this.timer);
            this.timer = null;
          }
        }, 1000); // 每隔一秒更新倒计时时间
      }
    },

    beforeDestroy() {
      if (this.timer) {
        // 组件销毁时清除计时器
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    onLoad(options) {
      var that = this;
      innerAudioContext = uni.createInnerAudioContext();
      innerAudioContext.onPlay(() => {
        console.log('开始播放');
      });
      innerAudioContext.onStop(function () {
        console.log('播放结束');
        that.isplay = false;
      });
      innerAudioContext.onPause(function () {
        console.log('播放暂停');
        that.isplay = false;
      });
      innerAudioContext.onError((res) => {
        console.log(res.errMsg);
        console.log(res.errCode);
        that.isplay = false;
      });

      this.studentCode = options.studentCode;
      this.deliverMerchant = options.deliverMerchant;
      this.merchantCode = options.merchantCode;
      // this.getReviewTime();
      this.loadWordList();
    },
    //下拉刷新
    onPullDownRefresh() {
      console.log('下拉刷新！！！！');
      this.pageindex = 1;
      this.loadWordList('refresh');
    },
    //加载更多
    onReachBottom() {
      this.pageindex++;
      this.loadWordList();
      console.log('加载更多！！！！');
    }
  };
</script>

<style>
  .ceshi {
    height: 600rpx;
    position: absolute;
    left: 50%;
    margin-left: -275rpx;
    top: 50%;
    margin-top: -300rpx;
    background: #fff;
    border-radius: 20rpx;
  }

  /* 按钮点击之后的颜色值#def4f2 */
  page {
    background-color: #f3f8fc !important;
    height: 100%;
  }

  .review-container {
    padding-bottom: 200rpx;
  }

  .txtbold {
    font-weight: 900;
    color: orange;
  }

  .hide {
    display: none;
  }

  .bgColor {
    background-color: #ffffff !important;
  }

  .review {
    width: 100%;
    min-height: 90rpx;
    border-bottom: 1px solid #e5e5e5;
    line-height: 90rpx;
    padding: 10rpx 0rpx;
    overflow: hidden;
  }

  .review-grey {
    background-color: #f0f2f5;
  }

  .review image {
    display: block;
    width: 50rpx;
    height: 50rpx;
    margin-top: 22rpx;
    margin-right: 30rpx;
  }

  .choose {
    display: flex;
    z-index: 0;
  }

  .review text {
    font-size: 32rpx;
    padding-left: 30rpx;
  }

  .translation {
    width: 230rpx;
    font-size: 30rpx;
    color: #333;
    margin-right: 20rpx;
  }

  .footer {
    width: 100%;
    height: 100rpx;
    background: #ffffff;
    border-top: 1px solid #e5e5e5;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  }
  .top-view {
    width: 100%;
    height: 160rpx;
    background: #f3f8fc;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
  }

  .submint {
    width: 180rpx;
    height: 70rpx;
    background: #007d70;
    border-radius: 35rpx;
    text-align: center;
    color: #ffffff;
    font-size: 30rpx;
    margin-right: 40rpx;
    line-height: 70rpx;
    margin-top: 14rpx;
  }

  .footer image {
    display: block;
    width: 50rpx;
    height: 50rpx;
  }

  .statistics {
    height: 100rpx;
    font-size: 30rpx;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 40rpx;
  }

  .word {
    margin-right: 30rpx;
    margin-left: 10rpx;
  }

  .Popup {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#99000000, endColorstr=#99000000);
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    z-index: 99;
  }

  .content {
    width: 530rpx;
    height: 560rpx;
  }

  .content image {
    width: 208rpx;
    height: 200rpx;
    display: block;
    margin: 0 auto;
    margin-top: 50rpx;
  }

  .remind {
    font-size: 30rpx;
    color: #333;
    text-align: center;
    margin-left: 30rpx;
    margin-right: 30rpx;
    display: block;
    margin-top: 50rpx;
  }

  .start {
    margin-left: 90rpx;
    margin-right: 90rpx;
    border-radius: 50%;
    color: #fff;
    font-size: 30rpx;
    height: 70rpx;
    background: #007d70;
    margin-top: 30rpx;
    line-height: 70rpx;
  }

  .temporarily {
    font-size: 28rpx;
    color: #999;
    text-align: center;
    margin-top: 20rpx;
  }

  .close {
    display: block;
    width: 46rpx;
    height: 46rpx;
    margin: 0 auto;
    margin-top: 20rpx;
    /* position: absolute;
		left: 50%;
		margin-left: -23rpx;
		top: 40rpx; */
  }

  .center-box {
    width: 500upx;
    height: 500upx;
    text-align: center;
  }

  .center-box .image {
    width: 100%;
    height: 100%;
  }

  .center-box text {
    font-size: 26rpx;
  }

  .fl {
    float: left;
  }

  .fr {
    float: right;
  }

  .zanwu {
    margin: 0 auto;
    margin-top: 50rpx;
    margin-bottom: 50rpx;
    text-align: center;
    font-size: 35rpx;
    color: #b3b7ba;
  }
</style>
