<template>
  <view>
    <view class="hear_img">
      <image src="https://document.dxznjy.com/app/images/zhujiaoduan/parent-bgc.png" class="wallet-bgc"></image>
      <view class="search-view1" v-if="navIndex == 1">
        <view class="search-date" style="width: 320rpx">
          <text style="padding-left: 10rpx; width: 180rpx; color: #999999; font-size: 30rpx; height: 80rpx; line-height: 80rpx" @click="searchDateShow">
            {{ searchDate ? searchDate : '选择日期' }}
          </text>
          <uni-icons v-if="searchDate == ''" type="right" color="#999999" size="20" @click="searchDateShow"></uni-icons>
          <uni-icons v-else type="close" color="#999999" size="20" @click="cleanSearchDate"></uni-icons>
        </view>
        <view class="search-student ml-20">
          <view style="display: flex">
            <image style="height: 30rpx; width: 30rpx; margin-top: 8rpx" src="/static/images/create_img_seacher.png"></image>
            <input type="text" v-model="searchName" placeholder="请输入学员姓名" class="search-student-input" />
          </view>
          <view class="search-student-text" @click="nameSearchFunc">搜索</view>
        </view>
      </view>
      <view class="search-view1" v-else>
        <view class="search-student" style="width: 610rpx">
          <view style="display: flex">
            <image style="height: 30rpx; width: 30rpx; margin-top: 8rpx" src="/static/images/create_img_seacher.png"></image>
            <input type="text" v-model="searchName" placeholder="请输入学员姓名" class="search-student-input" style="width: 410rpx" />
          </view>
          <view class="search-student-text" @click="nameSearchFunc">搜索</view>
        </view>
      </view>
      <view class="nav-title">复习课程表</view>
      <!--#ifdef APP-PLUS  -->
      <view class="backImg" @click="goBack">
        <image lazy-load src="/static/images/left-icon.png" style="width: 32rpx; height: 32rpx; display: block" mode=""></image>
      </view>
      <!-- #endif -->
    </view>
    <!-- tab栏 -->
    <view class="flex-c m-30">
      <u-tabs
        :list="tabsList"
        @click="tabClick"
        lineWidth="40rpx"
        lineColor="green"
        :activeStyle="{ color: '#000000', fontWeight: 'bold', fontSize: '32rpx' }"
        :inactiveStyle="{ color: '#666666', fontSize: '30rpx' }"
      ></u-tabs>
    </view>
    <view class="tab-bg">
      <view class="tab-ff-bg">
        <u-tabs
          :list="list4"
          @click="checkIndex"
          lineWidth="0"
          :inactiveStyle="{ color: '#666666', fontSize: '30rpx' }"
          :activeStyle="{ color: '#000000', fontWeight: 'bold', fontSize: '32rpx' }"
        ></u-tabs>
      </view>
    </view>

    <scroll-view
      v-if="studylist.length > 0"
      :style="'height: ' + (useHeight - 471) + 'rpx;'"
      style="background-color: #f3f8fc; padding-top: 1rpx"
      scroll-y="true"
      :scroll-top="scrollTop"
      @scrolltolower="scrolltolower"
    >
      <view class="scroll-box">
        <!-- 单词反馈 -->
        <view class="list-bg f-30 c-33" v-if="currentType == 'word'">
          <view v-for="(parentItem, parentIndex) in studylist" :key="parentIndex">
            <view class="f-32 c-00" style="font-weight: 600" :class="parentIndex == 0 ? 'pl-20 bg-ff pt-30' : 'pb-30'">
              <text class="date-title">{{ parentItem.date }}</text>
            </view>
            <view :class="parentIndex == 0 ? 'list-item-bg-0' : 'list-item-bg'">
              <view v-for="(item, index) in parentItem.data" :key="index">
                <view class="flex-dir-col flex-x-c ptb-30" :class="index == 0 ? '' : 'border-top'" v-if="navIndex == 0">
                  <view class="flex-s">
                    <view>{{ getMaxLengthText(item.studentName, 5) }}（{{ item.studentCode }}）</view>
                    <view v-if="item.startTime">开始时间：{{ item.startTime }}</view>
                  </view>
                  <view class="flex-a-c flex-x-s mt-30" v-if="getCurDay(parentItem.date)">
                    <view @click="antiForgetting(item)" class="c-2e8">21天抗遗忘</view>
                    <view class="c-2e8" style="margin: 0 50rpx">|</view>
                    <view @click="clickInterest(item)" class="c-2e8">趣味复习</view>
                  </view>
                </view>
                <view v-else class="flex-s" :class="index == 0 ? '' : 'border-top'" style="height: 120rpx">
                  <view>{{ getMaxLengthText(item.studentName, 6) }}（{{ item.studentCode }}）</view>
                  <view class="border-btn" @click="getStudentReviewFeedback(item)">查看反馈</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 拼音法列表 -->
        <view class="list-bg f-30 c-33" v-if="currentType == 'pyf'">
          <view v-for="(parentItem, parentIndex) in studylist" :key="parentIndex" v-if="navIndex == 0">
            <view class="f-32 c-00" style="font-weight: 600" :class="parentIndex == 0 ? 'pl-20 bg-ff pt-30' : 'pb-30'">
              <text class="date-title">{{ parentItem.date }}</text>
            </view>
            <view :class="parentIndex == 0 ? 'list-item-bg-0' : 'list-item-bg'">
              <view v-for="(item, index) in parentItem.data" :key="index" class="flex-dir-col flex-x-c ptb-30" :class="index == 0 ? '' : 'border-top'">
                <!-- {{item.studentName}} -->
                <view class="flex-s">
                  <view>{{ getMaxLengthText(item.studentName, 5) }}（{{ item.studentCode }}）</view>
                </view>
                <view class="flex-a-c flex-x-e mt-30 bor-d" v-if="getCurDay(item.shouldReviewTime)">
                  <view @click="pfyForgetting(item)" class="c-2e8 mr-15">拼音法抗遗忘</view>
                  <u-icon name="arrow-right"></u-icon>
                </view>
              </view>
            </view>
          </view>
          <view class="zanwu">没有更多数据了~</view>
        </view>
        <!-- 语法反馈 -->
        <view class="list-bg f-30 c-33" v-if="currentType == 'grammar'">
          <view v-for="(parentItem, parentIndex) in grammarStudylist" :key="parentIndex">
            <view class="f-32 c-00" style="font-weight: 600" :class="parentIndex == 0 ? 'pl-20 bg-ff pt-30' : 'pb-30'">
              <text class="date-title">{{ parentItem.date }}</text>
            </view>
            <view :class="parentIndex == 0 ? 'list-item-bg-0' : 'list-item-bg'" v-if="navIndex == 0">
              <view v-for="(item, index) in parentItem.data" :key="index">
                <view class="flex-dir-col flex-x-c ptb-30" :class="index == 0 ? '' : 'border-top'">
                  <view class="flex-s">
                    <view>{{ getMaxLengthText(item.studentName, 5) }}（{{ item.studentCode }}）</view>
                    <view v-if="item.startTime">开始时间：{{ item.startTime }}</view>
                    <view class="flex-a-c flex-x-s" v-if="getCurGra(item.reviewDate)">
                      <view @click="antiForgetting(item)" class="graButton">21天抗遗忘</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 已复习 -->
            <view v-else class="list-item-bg">
              <view>
                <view class="flex-dir-col flex-x-c" :class="index == 0 ? '' : 'border-top'">
                  <view class="flex-s ptb-30" v-for="(itemGra, indexGra) in parentItem.data" :key="indexGra">
                    <view class="">{{ getMaxLengthText(itemGra.studentName, 5) }}（{{ itemGra.studentCode }}）</view>
                    <view class="flex-a-c flex-x-s">
                      <view class="border-btn border-btnGra" @click="getStudentReview(itemGra)">语法反馈</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    <scroll-view
      v-else-if="grammarStudylist.length > 0"
      :style="'height: ' + (useHeight - 471) + 'rpx;'"
      style="background-color: #f3f8fc; padding-top: 1rpx"
      scroll-y="true"
      :scroll-top="scrollTop"
      @scrolltolower="scrolltolower"
    >
      <view class="scroll-box">
        <!-- 语法反馈 -->
        <view class="list-bg f-30 c-33" v-if="currentType == 'grammar'">
          <view v-for="(parentItem, parentIndex) in grammarStudylist" :key="parentIndex">
            <view class="f-32 c-00" style="font-weight: 600" :class="parentIndex == 0 ? 'pl-20 bg-ff pt-30' : 'pb-30'">
              <text class="date-title">{{ parentItem.date }}</text>
            </view>
            <view :class="parentIndex == 0 ? 'list-item-bg-0' : 'list-item-bg'" v-if="navIndex == 0">
              <view v-for="(item, index) in parentItem.data" :key="index">
                <view class="flex-dir-col flex-x-c ptb-30" :class="index == 0 ? '' : 'border-top'">
                  <view class="flex-s">
                    <view>{{ getMaxLengthText(item.studentName, 5) }}（{{ item.studentCode }}）</view>
                    <view v-if="item.startTime">开始时间：{{ item.startTime }}</view>
                    <view class="flex-a-c flex-x-s" v-if="getCurGra(item.reviewDate)">
                      <view @click="antiForgetting(item)" class="graButton">21天抗遗忘</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 已复习 -->
            <view v-else class="list-item-bg">
              <view>
                <view class="flex-dir-col flex-x-c" :class="index == 0 ? '' : 'border-top'">
                  <view class="flex-s ptb-30" v-for="(itemGra, indexGra) in parentItem.data" :key="indexGra">
                    <view class="">{{ getMaxLengthText(itemGra.studentName, 5) }}（{{ itemGra.studentCode }}）</view>
                    <view class="flex-a-c flex-x-s">
                      <view class="border-btn border-btnGra" @click="getStudentReview(itemGra)">语法反馈</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    <scroll-view
      v-else-if="pyflistOver.length > 0"
      :style="'height: ' + (useHeight - 471) + 'rpx;'"
      style="background-color: #f3f8fc; padding-top: 1rpx"
      scroll-y="true"
      :scroll-top="scrollTop"
      @scrolltolower="scrolltolower"
    >
      <view class="scroll-box">
        <!-- 拼音法列表 -->
        <view class="list-bg f-30 c-33" v-if="currentType == 'pyf'">
          <!-- 已复习 -->
          <block v-if="navIndex == 1">
            <view v-for="(parentItem, parentIndex) in pyflistOver" :key="parentIndex">
              <view class="f-32 c-00" style="font-weight: 600" :class="parentIndex == 0 ? 'pl-20 bg-ff pt-30' : 'pb-30'">
                <text class="date-title">{{ parentItem.time }}</text>
              </view>
              <view :class="parentIndex == 0 ? 'list-item-bg-0' : 'list-item-bg'">
                <view v-for="(item, index) in parentItem.value" :key="index" class="flex-s" :class="index == 0 ? '' : 'border-top'" style="height: 120rpx">
                  <view>{{ getMaxLengthText(item.studentName, 6) }}（{{ item.studentCode }}）</view>
                  <view class="border-btn" @click="getPyfFeedback(item)">查看反馈</view>
                </view>
              </view>
            </view>
          </block>

          <view class="zanwu">没有更多数据了~</view>
        </view>
      </view>
    </scroll-view>
    <scroll-view
      v-else-if="readList.length > 0"
      :style="'height: ' + (useHeight - 471) + 'rpx;'"
      style="background-color: #f3f8fc; padding-top: 1rpx"
      scroll-y="true"
      :scroll-top="scrollTop"
      @scrolltolower="scrolltolower"
    >
      <view class="scroll-box">
        <!-- 超级阅读列表 -->
        <view class="list-bg f-30 c-33" v-if="currentType == 'read'">
          <!-- 已复习 -->
          <view v-if="navIndex == 1">
            <view v-for="(item, index) in readList" :key="index">
              <view class="f-32 c-00" style="font-weight: 600" :class="index == 0 ? 'pl-20 bg-ff pt-30' : 'pb-30'">
                <text class="date-title">{{ item.reviewTime }}</text>
              </view>
              <view :class="index == 0 ? 'list-item-bg-0' : 'list-item-bg'">
                <view v-for="(item2, index1) in item.students" :key="index1" class="flex-s" :class="index == 0 ? '' : 'border-top'" style="height: 120rpx">
                  <view>{{ getMaxLengthText(item2.studentName, 6) }}（{{ item2.studentCode }}）</view>
                  <view class="border-btn" @click="getReadFeedback(item.reviewTime, item2)">查看反馈</view>
                </view>
              </view>
            </view>
            <!-- <view class="zanwu">没有更多数据了~</view> -->
          </view>
          <view v-else style="margin: 0 30rpx; width: 92%">
            <view class="" v-for="(item, index) in readList" :key="index">
              <view class="f-32 c-00" style="font-weight: 600" :class="index == 0 ? 'pl-20 bg-ff pt-30' : 'pb-30'">
                <text class="date-title">{{ item.reviewTime }}</text>
              </view>
              <view class="list-item-bg-1">
                <view v-for="item2 in item.students" class="readItems">
                  <view class="readItem">{{ getMaxLengthText(item2.studentName, 6) }}({{ item2.studentCode }})</view>
                  <view class="readBtn" v-if="index == 0 && getToday(item.reviewTime)" @click="goReadForget(item2)">超级阅读抗遗忘</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    <view v-else style="background-color: #f3f8fc; padding-bottom: 30rpx" :style="'height: ' + (useHeight - 500) + 'rpx;'">
      <view class="empty-content">
        <image :src="imgHost + '/app/images/zhujiaoduan/zhujiao_img_review_nodata.png'" class="empty-img"></image>
        <view style="color: #bdbdbd; margin-top: 28rpx">暂无记录</view>
      </view>
    </view>

    <!-- 选择校区弹窗 -->
    <uni-popup ref="popopChooseSchool" type="center">
      <view class="dialogBG">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="dialog-small-close" @click="cancelSchool">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="dialog-center">
            <view class="dialog-title bold pb-20">选择校区</view>
            <view
              class="dialog-item"
              @click="chooseSchoollist(item, index)"
              v-for="(item, index) in arraySchool"
              :key="index"
              :class="isactive == index ? 'selected' : 'not-selected'"
            >
              {{ item.merchantName }}
            </view>
            <view class="flex-c mt-40 mb-8">
              <view class="common-sure-btn-1" @click="confirmSchool()">确定</view>
              <view class="common-cancel-btn ml-40" @click="cancelSchool()">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 拼音法已复习 查看反馈 弹框 -->
    <uni-popup ref="popopPyfReport" type="center">
      <view class="dialogBG">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="dialog-small-close" @click="cancelPyfReport">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="dialog-center">
            <view class="dialog-title bold pb-20">选择报告</view>
            <view class="dialog-height">
              <view
                class="dialog-item"
                @click="choosePyfReportlist(item, index)"
                v-for="(item, index) in arrayPyfReport"
                :key="index"
                :class="isactive == index ? 'selected' : 'not-selected'"
              >
                {{ item.reviewTime }}
              </view>
            </view>
            <view class="flex-c mt-30 plr-20 sizing" style="height: 120rpx">
              <view class="common-sure-btn-1" @click="confirmPyfReport()">确定</view>
              <view class="common-cancel-btn ml-40" @click="cancelPyfReport()">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    <u-datetime-picker
      ref="dateChose"
      :show="showDateChose"
      v-model="searchDateModel"
      mode="date"
      itemHeight="40"
      confirmColor="#2e896f"
      @cancel="searchDateChosecancel"
      @confirm="searchDateChoseSure"
      :immediateChange="true"
    ></u-datetime-picker>

    <u-picker :show="showType" :columns="columns" @cancel="pickerCancel" @confirm="pickerChange"></u-picker>

    <uni-popup ref="reviewReport" type="center">
      <view class="dialogBG">
        <view class="dialog-all">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="dialog-small-close" @click="cancelReport">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="dialog-center">
            <view class="dialog-title bold pb-10">选择报告</view>
            <scroll-view style="height: 440rpx" scroll-y="true">
              <view class="scroll-box">
                <view
                  class="dialog-item"
                  @click="chooseReportlist(item, index)"
                  v-for="(item, index) in arrayReport"
                  :key="index"
                  :class="isactive == index ? 'selected' : 'not-selected'"
                >
                  {{ currentType == 'read' ? item.createTime : item.addTime }}
                </view>
              </view>
            </scroll-view>
            <view class="flex-c mt-40">
              <view class="common-sure-btn-1" @click="confirmReport()">确定</view>
              <view class="common-cancel-btn ml-40" @click="cancelReport()">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="loadingPopup" type="center" :is-mask-click="false">
      <view class="bg-ff radius-15 t-c">
        <view class="loadingpadding">
          <image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
          <view class="mt-30">加载中...</view>
          <view>请耐心等候</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import dayjs from 'dayjs';
  const lineBg =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAOCAYAAABdC15GAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFxSURBVHgBzZNRTsJAEIb/WTW+lpiY+FZPIDew3ABP4GJ8hxsI9zBpOYHeQDwBPQI+mRiRvpLojtPdYhCorQqF/6GdbGd2vvwzBXZcNAt4oj1ANeUoAT5iqkUjbEFLHNmhD1YPEvpZ3ghkGlVDCkc94/BmHMq998I5ONiY1ZBfpKAyuOtgAc5yOEDmYEWNh32BHF91sGHZHmwW4azciN9aQwnz3SJEgOmte+R2tdLprTYoa50mvuomlLpD4Y3oQZnov6D2RzCqI93bWOHaEmAGqQUyRBlZR1WfarcD/EJ2z8DtzDGvsMCwpm8XOCfDUsVOCYhiqRxI/CTQo4UOvjzO7Pow18vfywneuUHHUUxLn55lLw5JFpZ8bEUcY8oXdOLWiHLTxvoGpLqoUmy6dBT15o/ox3znpoycAmxUsiJTbs1cmxeVKp+0zmFIS7bGWiVghC7Vwse8jFKAX9eljh4ggKLLv7uaQvG9/F59Oo2SouxPu7OTCxN/s8wAAAAASUVORK5CYII=';
  export default {
    data() {
      return {
        tabsList: [
          {
            name: '单词'
          },
          {
            name: '拼音法'
          },
          {
            name: '语法'
          },
          {
            name: '超级阅读'
          }
        ], //tabs栏
        imgHost: 'https://document.dxznjy.com/',
        useHeight: 0, //屏幕高度
        list4: [
          {
            name: '未复习'
          },
          {
            name: '已复习'
          }
        ],
        navIndex: 0, // tabs栏索引

        searchName: '', //搜索名字

        showDateChose: false,
        searchDate: '', //选择日期
        searchDateModel: '', //选择日期弹框需要
        courseType: '', //课程类型
        showType: false, //选择类型弹框显示与隐藏
        columns: [['单词', '语法']], //选择类型弹框数据

        studylist: [], //列表数据
        grammarStudylist: [], // 新增语法数据存储变量
        pyflistOver: [], //拼音法已复习列表数据
        arrayPyfReport: [], //拼音法已复习报告列表
        readList: [], //超级阅读列表
        lastStudentCode: '', //上次请求的studentCode
        lastId: '', //上次请求的id

        lastDateStart: '', //上次请求的开始时间
        curReqStudentCode: '', //当前请求的studentCode
        curReqDateStart: '', //当前请求的开始时间
        lastDateStart: '', //上次请求的开始时间
        curReqStudentCode: '', //当前请求的studentCode
        curReqDateStart: '', //当前请求的开始时间

        scrollTop: 0, //滚动条位置
        scrollTop: 0, //滚动条位置

        isactive: -1, //当前选中的索引
        arraySchool: [], //校区列表
        cacheDialogMerchantCode: '', //上次请求的校区code
        choseInterestStudentItem: null, //当前选中的校区
        dialogMerchantCode: '', //当前请求的校区code
        isactive: -1, //当前选中的索引
        arraySchool: [], //校区列表
        cacheDialogMerchantCode: '', //上次请求的校区code
        choseInterestStudentItem: null, //当前选中的校区
        dialogMerchantCode: '', //当前请求的校区code

        arrayReport: [], //报告列表
        cacheReportCode: '', //上次请求的报告code
        reportCode: '', //当前请求的报告code
        userlist: null, //用户信息
        currentType: 'word', //当前请求的类型
        is21: false, //是否是21天
        showGrammarList: 0, //判断是否显示语法/拼音法/单词列表
        url: '', //当前请求的url
        pageNum: 1, //当前页码
        pageSize: 20, //每页显示多少条数据
        total: 0, //总条数
        hasMore: true,
        readstudentCode: '',
        merchantCode: '', //门店信息
        readTime: '',
        readReport: null
      };
    },
    watch: {
      searchName(newName, oldName) {
        if (newName.length == 0) {
          if (this.currentType == 'pyf') {
            this.getPyfList();
          }
          if (this.currentType == 'word') {
            this.getNormalList();
          }
          if (this.currentType == 'grammar') {
            this.getGraList();
          }
          if (this.currentType == 'read') {
            this.getReadList();
          }
        }
      }
    },
    onLoad() {
      this.searchDateModel = dayjs().format('YYYY-MM-DD');
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h;
        }
      });
      this.$refs.dateChose.setFormatter(this.formatter);
    },

    onShow() {
      this.searchName = '';
      this.searchDate = '';

      uni.removeStorage({
        key: 'logintokenReview'
      });

      this.clickHandle();
      this.getPersonalList();
      this.getNormalList();
      if (this.currentType == 'pyf') {
        this.getPyfList();
      }
      if (this.currentType == 'read') {
        this.getReadList();
      }
    },
    onReachBottom() {
      //触底事件
      if (this.pageNum * this.pageSize >= this.total) {
        uni.showToast({
          title: '没有更多数据了',
          icon: 'none',
          duration: 1000
        });
        setTimeout(() => {
          uni.hideLoading();
        }, 500);
      } else {
        if (this.pageNum <= this.pageNum - 1) {
          setTimeout(() => {
            uni.hideLoading();
          }, 500);
        } else {
          uni.showLoading({
            title: '加载中'
          });
          this.pageNum++;
          this.getReviewAlreadyList(true);
        }
        setTimeout(() => {
          uni.hideLoading();
        }, 500);
      }
    },
    methods: {
      goBack() {
        plus.runtime.quit();
      },
      getToday(dateString) {
        const today = new Date();
        const inputDate = new Date(dateString);

        // 清除时间部分，只保留日期
        const todayDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const inputDateOnly = new Date(inputDate.getFullYear(), inputDate.getMonth(), inputDate.getDate());

        return todayDate.getTime() === inputDateOnly.getTime();
      },
      tabClick(e) {
        this.readList = [];
        this.studylist = [];
        this.grammarStudylist = [];
        this.pyflistOver = [];
        // const typeMap = {
        // 	0: 'word',
        // 	1: 'grammar',
        // 	2: 'pyf'
        // }
        const typeMap = {
          0: 'word',
          1: 'pyf',
          2: 'grammar',
          3: 'read'
        };
        if (e.index in typeMap) {
          this.currentType = typeMap[e.index];
        }
        this.lastDateStart = '';
        this.lastStudentCode = '';
        this.lastId = ''; // 切换tabbar时清空lastId
        if (this.currentType === 'grammar') {
          this.showGrammarList = 2;
          this.getGraList();
        } else if (this.currentType == 'pyf') {
          this.showGrammarList = 1;
          this.getPyfList();
        } else if (this.currentType == 'read') {
          this.showGrammarList = 3;
          this.getReadList();
        } else {
          this.showGrammarList = 0;
          this.getNormalList();
        }
      },
      // searchTypeShow() {
      //   this.showType = true;
      // },
      async getReadFeedback(time, item) {
        let { data } = await uni.$http.get('/znyy/superReadReview/queryMerchantList?studentCode=' + item.studentCode);
        this.arraySchool = data.data;
        if (data.data.length == 1 && data.data != null) {
          this.merchantCode = this.arraySchool[0].merchantCode;
          let obj = {
            studentCode: item.studentCode,
            merchantCode: this.merchantCode,
            startTime: time
          };
          let { data } = await uni.$http.get('/znyy/superReadReview/getAlreadyReviewReportList', obj);
          if (data.success) {
            this.arrayReport = data.data;
            if (this.arrayReport == null || this.arrayReport.length == 0) {
              this.$util.alter('您还没有复习报告');
            } else {
              if (this.arrayReport.length == 1) {
                this.reportCode = this.arrayReport[0].id;
                uni.navigateTo({
                  url: `/ReadForget/readReport?studentCode=${item.studentCode}&merchantCode=${this.merchantCode}&forgettingId=${this.arrayReport[0].forgettingId}&type=2`
                });
              } else if (this.arrayReport.length > 1) {
                this.$refs.reviewReport.open();
              }
            }
          }
        } else if (data.data.length > 1 && data.data != null) {
          this.readstudentCode = item.studentCode;
          this.readTime = time;
          this.$refs.popopChooseSchool.open();
        } else {
          uni.showToast({
            title: '暂无记录',
            icon: 'none',
            duration: 3000
          });
        }
      },
      async goReadForget(e) {
        let { data } = await uni.$http.get('/znyy/superReadReview/queryMerchantList?studentCode=' + e.studentCode);
        this.arraySchool = data.data;
        if (data.data.length == 1 && data.data != null) {
          this.merchantCode = this.arraySchool[0].merchantCode;
          uni.navigateTo({
            url: '/ReadForget/index?merchantCode=' + this.merchantCode + '&studentCode=' + e.studentCode
          });
        } else if (data.data.length > 1 && data.data != null) {
          this.readstudentCode = e.studentCode;
          this.$refs.popopChooseSchool.open();
        } else {
          uni.showToast({
            title: '暂无记录',
            icon: 'none',
            duration: 3000
          });
        }
      },
      pickerChange(value) {
        const selectedType = value.value[0];
        if (selectedType === '单词' || selectedType === '语法') {
          this.courseType = selectedType;
          this.showType = false;
          this.getNormalList();
        } else {
          uni.showToast({
            icon: 'none',
            title: '选择的课程类型无效，请重新选择。'
          });
        }
      },
      pickerCancel() {
        this.showType = false;
      },
      formatter(type, value) {
        if (type === 'year') {
          return `${value}年`;
        }
        if (type === 'month') {
          return `${value}月`;
        }
        if (type === 'day') {
          return `${value}日`;
        }
        return value;
      },
      getCurDay(date) {
        return dayjs().isSame(date, 'day');
      },
      getCurGra(reviewDate) {
        return dayjs().isSame(reviewDate, 'day');
      },
      getMaxLengthText(str, len) {
        if (str.length > len) {
          let val = str.substr(0, len);
          return val + '...';
        }
        return str;
      },
      searchDateShow() {
        this.showDateChose = true;
      },
      searchDateChosecancel() {
        this.showDateChose = false;
      },
      searchDateChoseSure(e) {
        if (e) {
          this.searchDateModel = e.value;
          this.searchDate = dayjs(e.value).format('YYYY-MM-DD');

          this.getNormalList();
          if (this.currentType == 'grammar') {
            this.getGraList();
          }
          if (this.currentType == 'read') {
            this.getReadList();
          }
        }
        this.showDateChose = false;
      },
      cleanSearchDate() {
        this.searchDate = '';
        this.searchDateModel = dayjs().format('YYYY-MM-DD');
        this.getNormalList();
      },
      // 获取单词列表数据
      getNormalList() {
        this.lastDateStart = '';
        this.lastStudentCode = '';
        this.lastId = ''; // 重新加载时清空lastId
        this.studylist = [];
        if (this.navIndex == 1) {
          //已复习
          this.getReviewAlreadyList(true);
        } else {
          //未复习
          this.getReviewNoStudyList(true);
          console.log('单词未复习首次加载----------');
        }
      },
      // getReadList() {
      //   this.readList = [];
      //   // if (this.navIndex == 1) {
      //     //已复习
      //     this.getReadStudyList(true);
      //     // uni.hideLoading()
      //   // } else {
      //     //未复习
      //     // this.getReadStudyList(true);
      //     // uni.hideLoading()
      //     // console.log('超级阅读获取拼音法列表数据');
      //   }
      // },
      //超级阅读获取拼音法列表数据
      async getReadList() {
        this.studylist = [];
        this.readList = [];
        this.lastId = ''; // 重新加载时清空lastId
        let obj = {
          teachId: this.userlist.teacherCode,
          studentName: this.searchName,
          status: this.navIndex,
          startTime: this.searchDate
        };
        let { data } = await uni.$http.get('/znyy/superReadReview/queryReviewDataByTeach', obj);
        if (data.success) {
          this.readList = data.data;
        }
      },
      // 获取拼音法列表数据
      getPyfList() {
        this.studylist = [];
        this.pyflistOver = [];
        this.lastId = ''; // 重新加载时清空lastId
        if (this.navIndex == 1) {
          //已复习
          this.getReviewAlreadyList(true);
          // uni.hideLoading()
        } else {
          //未复习
          this.getPyfNoStudyList(true);
          // uni.hideLoading()
          console.log('拼音法未复习获取拼音法列表数据');
        }
      },
      getGraList() {
        this.grammarStudylist = [];
        this.lastId = ''; // 重新加载时清空lastId
        if (this.navIndex == 1) {
          //已复习
          this.getGraReviewAlreadyList(true);
          console.log('已复习获取语法列表数据');
        } else {
          //未复习
          this.getGraReviewNoStudyList(true);
          console.log('复习获取语法列表数据');
        }
      },

      // 滚动条回到顶部
      clickHandle() {
        this.scrollTop = this.scrollTop === 0 ? 1 : 0;
      },
      // 滚动条上拉加载
      scrolltolower() {
        if (this.currentType == 'pyf') {
          if (this.navIndex == 0) {
            if (!this.hasMore) {
              return;
            }
            this.pageNum += 1;
            console.log('this.pageNum', this.pageNum);

            // 表示不是第一次请求
            this.getPyfNoStudyList(false);
          }
        } else if (this.currentType == 'grammar') {
          if (this.lastDateStart != '' && this.lastStudentCode != '') {
            if (this.navIndex == 1) {
              //已复习
              this.getGraReviewAlreadyList(false);
            } else {
              //未复习
              this.getGraReviewNoStudyList(false);
            }
          }
        } else if (this.currentType == 'read') {
        } else {
          if (this.lastDateStart != '' && this.lastStudentCode != '') {
            if (this.navIndex == 1) {
              //已复习
              this.getReviewAlreadyList(false);
            } else {
              //未复习
              this.getReviewNoStudyList(false);
            }
          }
        }
      },
      nameSearchFunc() {
        if (this.currentType == 'pyf') {
          this.pageNum = 1;
          this.getPyfList();
        } else if (this.currentType == 'grammar') {
          this.getGraList();
        } else if (this.currentType == 'read') {
          this.getReadList();
        } else {
          this.getNormalList();
        }
      },
      // 语法未复习
      async getGraReviewNoStudyList(isFirst) {
        console.log('未复习', this.showGrammarList);
        let that = this;
        if (that.lastStudentCode != '' && that.curReqStudentCode == that.lastStudentCode && that.curReqDateStart == that.lastDateStart) {
          return;
        }
        that.curReqDateStart = that.lastDateStart;
        that.curReqStudentCode = that.lastStudentCode;
        if (that.showGrammarList === 2) {
          // 语法
          // that.lastStudentCode = '6231217888'
          this.url = `/dyf/wap/applet/notReview?studentCode=${that.lastStudentCode}&pageNum=${this.pageNum}&pageSize=${this.pageSize}&studentName=${that.searchName}`;
          let res = await uni.$http.get(this.url);
          // this.pageSize = res.data.data.totalItems
          if (res) {
            console.log('----', res.data, this.navIndex);
            if (this.navIndex == 1) {
              return;
            }
            let resData = res.data.data.data;
            console.log(resData, 'resData');
            if (!isFirst) {
              console.log(1111);
              let old = that.grammarStudylist;
              that.grammarStudylist = this.studyDateOption(old, resData);
            } else {
              console.log(2222);
              that.grammarStudylist = res.data.data;
              that.grammarStudylist = this.studyDateOption(null, resData);
              console.log(this.currentType, this.grammarStudylist);
            }
          }
        }
      },

      // 单词未复习
      async getReviewNoStudyList(isFirst) {
        console.log('未复习', this.showGrammarList);
        let that = this;
        if (that.lastStudentCode != '' && that.curReqStudentCode == that.lastStudentCode && that.curReqDateStart == that.lastDateStart) {
          return;
        }
        that.curReqDateStart = that.lastDateStart;
        that.curReqStudentCode = that.lastStudentCode;
        if (that.showGrammarList === 0) {
          if (this.showGrammarList !== 0) return;
          let res = await uni.$http.get(
            `/deliver/app/teacher/getTeacherReviewNotStudyListNew?studentCode=${that.lastStudentCode}&dateStart=${that.lastDateStart}&studentName=${that.searchName}&id=${that.lastId}`
          );
          if (res) {
            if (this.currentType !== 'word') {
              return;
            }
            if (this.navIndex == 1) {
              return;
            }
            let resData = res.data.data;
            if (!isFirst) {
              let old = that.studylist;
              that.studylist = this.studyDateOption(old, resData);
            } else {
              that.studylist = res.data.data;
              that.studylist = this.studyDateOption(null, resData);
            }
            if (resData.length == 0) {
              that.lastDateStart = '';
              that.lastStudentCode = '';
              that.lastId = '';
            } else {
              that.lastDateStart = resData[resData.length - 1].date;
              that.lastStudentCode = resData[resData.length - 1].studentCode;
              that.lastId = resData[resData.length - 1].id;
            }
          }
        }
      },

      // 拼音法未复习
      async getPyfNoStudyList(isFirst) {
        let that = this;
        // if (
        // 	that.lastStudentCode != '' &&
        // 	that.curReqStudentCode == that.lastStudentCode &&
        // 	that.curReqDateStart == that.lastDateStart
        // ) {
        // 	// 如果当前请求的学生代码、日期开始时间和上次的请求值相同，则直接返回，避免重复请求
        // 	return
        // }
        let url;
        let params = {
          studentName: that.searchName,
          teacherId: that.userlist.teacherCode,
          pageNum: that.pageNum,
          pageSize: that.pageSize
        };
        url = '/znyy/pd/planReview/queryPlanNotReview';
        that.$http.post(url, params).then((res) => {
          if (that.navIndex == 1) {
            return;
          }

          if (this.showGrammarList != 1) {
            return;
          }
          let resData = res.data.data;
          console.log('resData------', resData);
          if (resData.length == 0) {
            this.hasMore = false;
            console.log('不加载');
          }
          if (!isFirst) {
            let old = that.studylist;
            that.studylist = that.pyfStudyDateOption(old, resData);
          } else {
            that.studylist = res.data.data;
            that.studylist = that.pyfStudyDateOption(null, resData);
          }
          console.log('that.studylist----------', that.studylist);
        });
      },

      pyfStudyDateOption(oldData, newData) {
        let dataArr = [];
        if (oldData) {
          dataArr = oldData;
        }
        for (let i = 0; i < newData.length; i++) {
          if (!newData[i].shouldReviewTime) {
            continue;
          }
          let dataTime = dayjs(newData[i].shouldReviewTime).format('YYYY-MM-DD');
          let haveIndex = this.getIsHaveDate(dataArr, dataTime);
          if (haveIndex != -1) {
            dataArr[haveIndex].data.push(newData[i]);
          } else {
            let needData = {
              date: dataTime,
              data: [newData[i]]
            };
            dataArr.push(needData);
          }
        }
        return dataArr;
      },

      // 语法已复习
      async getGraReviewAlreadyList(isFirst) {
        console.log('已复习列表', this.showGrammarList);
        let that = this;
        if (that.lastStudentCode != '' && that.curReqStudentCode == that.lastStudentCode && that.curReqDateStart == that.lastDateStart) {
          return;
        }
        that.curReqDateStart = that.lastDateStart;
        that.curReqStudentCode = that.lastStudentCode;
        if (this.showGrammarList == 2) {
          // 语法
          this.url = `/dyf/wap/applet/reviewed?date=${that.searchDate}&studentCode=${that.lastStudentCode}&pageNum=${this.pageNum}&pageSize=${this.pageSize}&studentName=${that.searchName}`;
          let res = await uni.$http.get(this.url);
          // this.pageSize = res.data.data.totalItems
          if (res) {
            if (this.navIndex == 0) {
              return;
            }
            if (this.showGrammarList != 2) {
              return;
            }
            let resData = res.data.data.data;
            if (!isFirst) {
              let old = that.grammarStudylist;
              that.grammarStudylist = this.studyDateOption(old, resData);
            } else {
              that.grammarStudylist = res.data.data;
              that.grammarStudylist = this.studyDateOption(null, resData);
            }
          }
        }
      },

      // 单词已复习列表
      async getReviewAlreadyList(isFirst) {
        console.log('已复习列表', this.showGrammarList);
        let that = this;
        if (that.lastStudentCode != '' && that.curReqStudentCode == that.lastStudentCode && that.curReqDateStart == that.lastDateStart) {
          return;
        }
        that.curReqDateStart = that.lastDateStart;
        that.curReqStudentCode = that.lastStudentCode;
        if (this.showGrammarList == 1) {
          // 拼音法
          let params = {
            studentName: this.searchName,
            teacherId: this.userlist.teacherCode
          };
          params.dateStr = this.searchDate;
          this.url = '/znyy/pd/planReview/queryStudentPastReviewed';
          let res = await this.$http.post(this.url, params);
          const newData = res.data.data;
          if (res) {
            if (this.navIndex == 0) {
              return;
            }
            if (this.showGrammarList != 1) {
              return;
            }
            let resData = res.data.data;
            let arr = [];
            for (let key in resData) {
              let obj = {};
              console.log(key, resData[key]);
              obj.time = key;
              obj.value = resData[key];
              arr.push(obj);
            }
            that.studylist = [];
            that.grammarStudylist = [];
            that.pyflistOver = arr;
          }
        } else if (this.showGrammarList == 0) {
          // 如果是首次加载（从未复习切换到已复习），不传递lastId
          // 如果是滚动加载，传递lastId
          const idParam = isFirst ? '' : that.lastId;
          this.url = `/deliver/app/teacher/getTeacherReviewAlreadyStudyListNew?date=${that.searchDate}&studentCode=${that.lastStudentCode}&dateStart=${that.lastDateStart}&studentName=${that.searchName}&id=${idParam}`;
          let res = await uni.$http.get(this.url);
          if (res) {
            if (this.navIndex == 0) {
              return;
            }
            if (this.showGrammarList != 0) {
              return;
            }
            let resData = res.data.data;
            if (!isFirst) {
              let old = that.studylist;
              that.studylist = this.studyDateOption(old, resData);
            } else {
              that.studylist = res.data.data;
              that.studylist = this.studyDateOption(null, resData);
            }
            if (resData.length == 0) {
              that.lastDateStart = '';
              that.lastStudentCode = '';
              that.lastId = '';
            } else {
              that.lastDateStart = resData[resData.length - 1].date;
              that.lastStudentCode = resData[resData.length - 1].studentCode;
              that.lastId = resData[resData.length - 1].id;
            }
          }
        }
      },

      studyDateOption(oldData, newData) {
        let dataArr = [];
        if (oldData) {
          dataArr = oldData;
        }
        for (let i = 0; i < newData.length; i++) {
          let dataTime = newData[i].date ? newData[i].date : newData[i].reviewDate;
          let haveIndex = this.getIsHaveDate(dataArr, dataTime);
          if (haveIndex != -1) {
            dataArr[haveIndex].data.push(newData[i]);
          } else {
            let needData = {
              date: newData[i].date ? newData[i].date : newData[i].reviewDate,
              data: [newData[i]]
            };
            dataArr.push(needData);
          }
        }
        console.log(dataArr);
        return dataArr;
      },
      getIsHaveDate(arr, date) {
        for (var i = 0; i < arr.length; i++) {
          if (arr[i].date == date) {
            return i;
          }
        }
        return -1;
      },
      // 点击复习/未复习
      checkIndex(e) {
        // 点击未复习/复习的时候初始值至为1
        this.pageNum = 1;
        this.navIndex = e.index;
        this.lastDateStart = '';
        this.lastStudentCode = '';
        this.searchName = '';
        // 切换未复习/已复习时清空lastId
        this.lastId = '';
        console.log(this.currentType, 'mmmmmmmmmmmmmmmmmmmmmmmmmmmm');
        if (this.currentType === 'grammar') {
          this.showGrammarList = 2;
          this.getGraList();
        } else if (this.currentType == 'pyf') {
          this.showGrammarList = 1;
          this.getPyfList();
          console.log('this.navIndex----------', this.navIndex);
        } else if (this.currentType == 'read') {
          this.showGrammarList = 3;
          this.getReadList();
        } else {
          this.showGrammarList = 0;
          this.getNormalList();
        }
      },

      // 获取个人信息(账号，姓名)
      async getPersonalList() {
        let { data } = await uni.$http.get('/deliver/app/teacher/info');
        this.userlist = data.data;
        // debugger
        uni.getStorageSync('teacherName', this.userlist.name);
        uni.getStorageSync('teacherCode', this.userlist.teacherCode);
        uni.getStorageSync('merchantName', this.userlist.merchantName);
        uni.getStorageSync('merchantCode', this.userlist.merchantCode);
      },

      //21天抗遗忘
      antiForgetting(item) {
        this.is21 = true;
        this.choseInterestStudentItem = item;
        // this.getSchoolList(item.studentCode) //复习包去除 无需门店信息
        this.jumpTo21();
      },

      jumpTo21() {
        if (this.choseInterestStudentItem) {
          uni.navigateTo({
            url:
              '/antiForgetting/index?buttonclickName=' +
              this.choseInterestStudentItem.studentName +
              '&buttonClick=' +
              encodeURIComponent(this.choseInterestStudentItem.studentCode) +
              '&merchantCode=&deliverMerchant=' +
              '&type=' +
              this.currentType
          });
        }
      },
      jumpTo5() {
        if (this.choseInterestStudentItem) {
          uni.navigateTo({
            url: '/PYFpages/forgetReview'
          });
        }
      },
      pfyForgetting(item) {
        this.is21 = true;
        this.choseInterestStudentItem = item;
        // this.getSchoolList(item.studentCode) //复习包去除 无需门店信息
        uni.setStorageSync('pyfStudentCode', item.studentCode);
        console.log('uni.setStorageSyncpyfStudentCode', item.studentCode);
        this.jumpTo5();
      },
      //选择校区
      chooseSchoollist(item, index) {
        this.isactive = index;
        this.cacheDialogMerchantCode = item.merchantCode;
      },
      //选择已复习报告
      choosePyfReportlist(item, index) {
        this.isactive = index;
        this.planReviewId = item.id;
        // this.planReviewId = '1278040432582873088'
      },
      confirmPyfReport() {
        if (this.isactive == -1) {
          uni.showToast({
            icon: 'none',
            title: '请先选择~'
          });
          return;
        }
        this.reportCode = this.planReviewId;
        this.$refs.popopPyfReport.close();
        this.jumpPyfReport();
        this.isactive = -1;
        this.planReviewId = '';
      },
      cancelPyfReport() {
        this.$refs.popopPyfReport.close();
        this.isactive = -1;
        this.planReviewId = '';
      },
      jumpPyfReport() {
        // 拼音法复习报告
        uni.navigateTo({
          url: '/PYFpages/reviewReport?planReviewId=' + this.planReviewId + '&toIndex=' + 1
        });
      },
      async confirmSchool() {
        if (this.isactive == -1) {
          uni.showToast({
            icon: 'none',
            title: '请先选择~'
          });
          return;
        }
        if (this.currentType == 'read' && this.navIndex == 0) {
          uni.navigateTo({
            url: '/ReadForget/index?merchantCode=' + this.cacheDialogMerchantCode + '&studentCode=' + this.readstudentCode
          });
          this.$refs.popopChooseSchool.close();
          this.isactive = -1;
          this.readstudentCode = '';
          this.cacheDialogMerchantCode = '';
          return;
        }
        if (this.currentType == 'read' && this.navIndex == 1) {
          let obj = {
            studentCode: this.readstudentCode,
            merchantCode: this.cacheDialogMerchantCode,
            startTime: this.readTime
          };
          let { data } = await uni.$http.get('/znyy/superReadReview/getAlreadyReviewReportList', obj);
          if (data.success) {
            this.arrayReport = data.data;
            if (this.arrayReport == null || this.arrayReport.length == 0) {
              this.$util.alter('您还没有复习报告');
            } else {
              if (this.arrayReport.length == 1) {
                this.reportCode = this.arrayReport[0].id;
                uni.navigateTo({
                  url: `/ReadForget/readReport?studentCode=${this.readstudentCode}&merchantCode=${this.cacheDialogMerchantCode}&forgettingId=${this.arrayReport[0].forgettingId}&type=2`
                });
              } else if (this.arrayReport.length > 1) {
                this.$refs.reviewReport.open();
              }
            }
          }
          this.$refs.popopChooseSchool.close();
          this.isactive = -1;
          this.readTime = '';
          this.readstudentCode = '';
          this.merchantCode = '';
          this.cacheDialogMerchantCode = '';
          return;
        }
        this.dialogMerchantCode = this.cacheDialogMerchantCode;
        if (this.is21) {
          this.jumpTo21();
        } else {
          this.getPadToken();
        }
        this.$refs.popopChooseSchool.close();
        this.isactive = -1;
        this.cacheDialogMerchantCode = '';
      },
      cancelSchool() {
        this.$refs.popopChooseSchool.close();
        this.isactive = -1;
        this.cacheDialogMerchantCode = '';
      },

      //趣味复习
      interest() {
        if (this.choseInterestStudentItem) {
          uni.navigateTo({
            url: '/interestModule/funReview?studentCode=' + this.choseInterestStudentItem.studentCode + '&merchantCode=' + this.dialogMerchantCode
          });
        }
      },
      clickInterest(item) {
        this.is21 = false;
        this.choseInterestStudentItem = item;
        this.getSchoolList(item.studentCode);
      },
      //获取校区
      async getSchoolList(studentCode) {
        let that = this;
        that.$refs.loadingPopup.open();
        try {
          let result = await uni.$http.get('/v2/mall/getStudentMerchantList?studentCode=' + studentCode);
          that.$refs.loadingPopup.close();
          if (result.data.success) {
            that.arraySchool = result.data.data;
            if (result.data.data == null) {
              that.$util.alter('您还没有学习过课程');
            } else {
              if (result.data.data.length == 1) {
                that.dialogMerchantCode = that.arraySchool[0].merchantCode;
                if (this.is21) {
                  that.jumpTo21();
                } else {
                  that.getPadToken();
                }
              } else if (result.data.data.length > 1) {
                that.$refs.popopChooseSchool.open();
              }
            }
          } else {
            that.$util.alter(result.data.message);
          }
        } catch (e) {
          that.$refs.loadingPopup.close();
        }
      },
      // 获取pad token
      async getPadToken() {
        let that = this;
        var logintoken = uni.getStorageSync('token');
        that.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get(
            `/new/security/v2/login/student/member/token?memberToken=${logintoken}&studentCode=${that.choseInterestStudentItem.studentCode}&merchantCode=${that.dialogMerchantCode}`
          );
          that.$refs.loadingPopup.close();
          if (res.data.success) {
            uni.setStorageSync('logintokenReview', res.data.data.token);
            that.interest();
          } else {
            that.$util.alter(res.data.message);
          }
        } catch (e) {
          that.$refs.loadingPopup.close();
        }
      },
      getStudentReview(item) {
        uni.navigateTo({
          url: `/antiForgetting/hisGraReviewReport?studentCode=${item.studentCode}&handoutId=${item.id}`
        });
      },
      async getStudentReviewFeedback(item) {
        let that = this;
        that.$refs.loadingPopup.open();
        this.isactive = -1;
        try {
          let data = {
            pageNum: 1,
            pageSize: 100,
            studentCode: item.studentCode,
            startTime: item.date + ' 00:00:00',
            endTime: item.date + ' 23:59:59'
          };
          let res = await uni.$http.get(`/znyy/areas/student/course/student/word/review/1/100/${item.studentCode}`, data);
          that.$refs.loadingPopup.close();
          if (res.data.success) {
            that.arrayReport = res.data.data.data;
            if (that.arrayReport == null || that.arrayReport.length == 0) {
              that.$util.alter('您还没有复习报告');
            } else {
              if (that.arrayReport.length == 1) {
                that.reportCode = that.arrayReport[0].id;
                this.jumpToReport();
              } else if (that.arrayReport.length > 1) {
                that.$refs.reviewReport.open();
              }
            }
          } else {
            that.$util.alter(res.data.message);
          }
        } catch (e) {
          that.$refs.loadingPopup.close();
        }
      },
      formatDateToLocal(dateInput, timeZoneOffset = 8) {
        const date = new Date(dateInput);

        // 转为毫秒后加上时区偏移（单位：毫秒）
        const localTime = new Date(date.getTime() + timeZoneOffset * 60 * 60 * 1000);

        const year = localTime.getUTCFullYear();
        const month = String(localTime.getUTCMonth() + 1).padStart(2, '0');
        const day = String(localTime.getUTCDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
      },
      async getPyfFeedback(item) {
        this.$refs.popopPyfReport.open();
        let result = await this.$http.post('/znyy/pd/planReview/queryReviewedFeedback', {
          dateStr: this.formatDateToLocal(item.reviewTime),
          // dateStr: new Date(item.reviewTime).toISOString().split('T')[0],
          studentCode: item.studentCode
        });
        console.log('result', result);
        this.arrayPyfReport = result.data.data;
        console.log('this.arrayPyfReport0', this.arrayPyfReport);
      },
      //选择
      chooseReportlist(item, index) {
        this.isactive = index;
        if (this.currentType == 'read') {
          this.readReport = item;
        } else {
          this.cacheReportCode = item.id;
        }
      },
      confirmReport(e) {
        if (this.isactive == -1) {
          uni.showToast({
            icon: 'none',
            title: '请先选择~'
          });
          return;
        }

        if (this.currentType == 'read') {
          uni.navigateTo({
            url: `/ReadForget/readReport?studentCode=${this.readReport.studentCode}&merchantCode=${this.readReport.merchantCode}&forgettingId=${this.readReport.forgettingId}&type=2`
          });
          this.$refs.reviewReport.close();
          this.isactive = -1;
          this.readReport = null;
          return;
        }
        this.reportCode = this.cacheReportCode;
        this.$refs.reviewReport.close();
        this.jumpToReport();

        this.cacheReportCode = '';
      },
      cancelReport() {
        this.$refs.reviewReport.close();
        this.isactive = -1;
        this.cacheReportCode = '';
      },
      jumpToReport() {
        // 单词复习报告
        // uni.navigateTo({
        //   url: `/antiForgetting/historyReviewReport?reviewId=${this.reportCode}`
        // });
        uni.navigateTo({
          url: `/antiForgetting/historyReviewReport?reviewId=${this.reportCode}`
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .scroll-box {
    margin: 0 auto;
    width: 100%;
  }
  .hear_img {
    position: relative;
    width: 100%;
    height: 340rpx;
    z-index: 99;
  }
  .readItems {
    border-bottom: 2rpx dashed #efefef;
    padding-bottom: 10rpx;
    &:last-child {
      border: none;
    }
  }
  .hear_img1 {
    position: relative;
    width: 100%;
    height: 455rpx;
    z-index: 99;
  }

  .wallet-bgc {
    width: 100%;
    height: 340rpx;
  }

  .nav-title {
    position: absolute;
    font-weight: bold;
    font-size: 34rpx;
    top: 130rpx;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 999;
  }
  .backImg {
    position: absolute;
    top: 130rpx;
    left: 5%;
    transform: translate(-50%, -50%);
    z-index: 999;
  }
  .search-view {
    position: absolute;
    left: 50%;
    transform: translate(-50%, -50%);
    top: 316rpx;
    width: 690rpx;
    height: 220rpx;
    background-color: #fff;
    border-radius: 14rpx;
    display: flex;
    flex-direction: column;
  }

  .search-view1 {
    position: absolute;
    left: 50%;
    transform: translate(-50%, -50%);
    top: 270rpx;
    width: 690rpx;
    height: 140rpx;
    background-color: #fff;
    border-radius: 14rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .search-date {
    width: 608rpx;
    height: 80rpx;
    display: flex;
    border-radius: 8rpx;
    background-color: rgba(200, 200, 200, 0.18);
    justify-content: space-between;
    align-items: center;
  }

  .search-student {
    // width: 390rpx;
    height: 80rpx;
    display: flex;
    border-radius: 8rpx;
    background-color: rgba(200, 200, 200, 0.18);
    justify-content: space-around;
    align-items: center;
  }

  .search-student-input {
    color: #999999;
    margin-left: 6rpx;
    width: 210rpx;
    height: 40rpx;
    font-size: 30rpx;
  }

  .search-student-text {
    padding-left: 20rpx;
    color: #2e896f;
    font-size: 30rpx;
    border-left: 1rpx solid #d9d9d9;
  }

  .tab-bg {
    background-color: #f3f8fc;
    width: 100%;
    height: 100rpx;
    padding-top: 10rpx;
    display: flex;
    justify-content: center;
  }

  .tab-ff-bg {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-left: 30rpx;
    margin-right: 30rpx;
    background-color: #fff;
    height: 100rpx;
    border-top-left-radius: 14rpx;
    border-top-right-radius: 14rpx;
    border-bottom: 1rpx solid #eeeeee;
  }

  .list-bg {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f3f8fc;
    width: 100%;
  }

  .list-item-bg {
    width: 650rpx;
    background: #ffffff;
    border-radius: 14rpx;
    margin-bottom: 30rpx;
    padding: 0 20rpx 1rpx 20rpx;
  }

  .list-item-bg-0 {
    width: 650rpx;
    background: #ffffff;
    border-bottom-left-radius: 14rpx;
    border-bottom-right-radius: 14rpx;
    margin-bottom: 30rpx;
    padding: 0 20rpx 1rpx 20rpx;
  }
  .list-item-bg-1 {
    width: 650rpx;
    background: #ffffff;
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    padding: 20rpx;
  }

  .readItem {
    height: 92rpx;
    line-height: 92rpx;
  }
  .readBtn {
    height: 40rpx;
    line-height: 40rpx;
    color: #339378;
    font-size: 28rpx;
  }
  .flex-between {
    margin-right: 20rpx;
    margin-left: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .border-top {
    border-top: 2rpx dashed #eeeeee;
  }

  .loadingImg {
    width: 120rpx;
    height: 120rpx;
  }

  .loadingpadding {
    padding: 85rpx 250rpx;
  }

  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dialogBG {
    margin: 0 30rpx 30rpx 30rpx;
    background-color: #fff;
    border-radius: 14rpx;
  }

  .input-border {
    height: 80rpx;
    border-radius: 14rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.2);
  }

  .search-dialog-student-input {
    color: #666666;
    margin-left: 30rpx;
    width: 460rpx;
    height: 40rpx;
    font-size: 28rpx;
  }

  .common-sure-btn {
    width: 250rpx;
    height: 80rpx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 40rpx;
    font-size: 30rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .common-sure-btn-1 {
    width: 250rpx;
    height: 80rpx;
    background: #2e896f;
    border-radius: 40rpx;
    font-size: 30rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
  /deep/.u-tabs__wrapper__nav__item {
    height: 80rpx !important;
  }
  .common-cancel-btn {
    width: 250rpx;
    height: 80rpx;
    border-radius: 40rpx;
    border: 1rpx solid #2e896f;
    font-size: 30rpx;
    color: #2e896f;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }

  .border-btn {
    width: 160rpx;
    height: 60rpx;
    border-radius: 30rpx;
    border: 1rpx solid #2e896f;
    font-size: 30rpx;
    color: #2e896f;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }

  .border-btnGra {
    height: 45rpx;
  }

  .graButton {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2e896f;
    width: 160rpx;
    height: 40rpx;
    line-height: 40rpx;
    text-align: center;
    border: 1rpx solid #2e896f;
    border-radius: 20rpx;
    font-size: 30rpx;
  }

  .empty-content {
    height: 100%;
    margin: 0 30rpx 0 30rpx;
    z-index: 9;
    border-bottom-left-radius: 14rpx;
    border-bottom-right-radius: 14rpx;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    /* 垂直布局，子视图按列排列 */
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    /* 垂直居中 */
  }

  .empty-img {
    width: 128rpx;
    height: 128rpx;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 170rpx;
    z-index: -1;
  }

  .dialog-all {
    width: 670rpx;
    position: relative;
  }

  .dialog-all image {
    width: 100%;
    height: 100%;
  }

  .dialog-center {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }

  .dialog-small-close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .dialog-title {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    font-weight: 600;
    justify-content: center;
  }

  .dialog-item {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .dialog-height {
    max-height: 500rpx;
    overflow: auto;
    // display: flex;
  }

  .selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    overflow: visible;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    // overflow: visible;
    display: block;
    border-radius: 35rpx;
    transform: scale(0.995); /* 解决ios上圆角失效 */
  }
</style>
