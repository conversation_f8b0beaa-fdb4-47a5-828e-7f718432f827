<template>
  <!-- <page-meta :page-style=""></page-meta> -->
  <view>
    <view class="container">
      <image src="https://document.dxznjy.com/Assistant/home_hear_1.png" class="hear_img"></image>
      <!--#ifdef APP-PLUS  -->
      <view class="backImg" @click="goBack">
        <image lazy-load src="/static/images/left-icon.png" style="width: 32rpx; height: 32rpx; display: block" mode=""></image>
      </view>
      <!-- #endif -->
      <view class="nav-title">
        <view class="status_bar">个人中心</view>
      </view>
      <view class="personalBox">
        <view class="pic">
          <!-- <button class="userImg" :open-type="chooseAvatar"> -->
          <button class="userImg" @click="changeAvatar">
            <image class="userImg" :src="avatarUrl"></image>
          </button>
          <!-- <open-data type="userAvatarUrl"></open-data> -->
        </view>
        <view>
          <view>
            <text style="font-weight: 600; font-size: 34rpx; color: #000000">{{ userlist.name }}</text>
            <text class="mobile_style ml-20">{{ userlist.mobile || '暂未授权' }}</text>
          </view>
          <view style="margin-top: 17rpx">
            <text class="merchantName-style">{{ userlist.merchantName }}</text>
          </view>
        </view>
        <view>
          <button v-if="loginShow" class="backbtn" type="primary" default>登录</button>
        </view>
      </view>

      <view class="c-ff money">
        <view class="flex-s">
          <view class="f-30">
            账期余额：
            <text class="f-36">{{ remainder || 0.0 }}</text>
          </view>
          <view class="details f-28" @click="goUrl(`/pages/my/wallet/index?userCode=${userlist.userCode}&teacherName=${userlist.name}`)">查看详情</view>
        </view>
        <view class="mt-25 c-da f-24">次月10号可提现上月以及之前的账期余额</view>
      </view>
    </view>

    <!-- 功能列表 -->
    <view class="my_list">
      <scroll-view :style="{ height: useHeight + 'rpx' }" scroll-y="true">
        <view>
          <view class="listItem" @click="goUrl('/news/news')">
            <image class="listItem_icon" src="https://document.dxznjy.com/Assistant/home_news.png"></image>
            <view class="itemInner">
              <text>消息</text>
            </view>
          </view>
          <view class="listItem" @click="goUrl('/news/availableTime')">
            <image class="listItem_icon" src="https://document.dxznjy.com/Assistant/home_time.png"></image>
            <view class="itemInner">
              <text>可用时间</text>
            </view>
          </view>
          <view class="listItem" @click="goUrl('/authen/authen?userCode=' + userlist.userCode)">
            <image class="listItem_icon" src="https://document.dxznjy.com/Assistant/home_authen.png"></image>
            <view class="itemInner">
              <text>实名认证</text>
            </view>
          </view>
          <!-- #ifdef MP-WEIXIN -->
          <view class="listItem" @click="openPopup">
            <image class="listItem_icon" src="https://document.dxznjy.com/manage/1718607597000"></image>
            <view class="itemInner">
              <text>建群</text>
            </view>
          </view>
          <!-- #endif -->
          <!-- #ifdef APP-PLUS    -->
          <view class="listItem" v-if="userRole == 'DeliverTeamLeader'" @click="goUrl('/qyWechat/coachList')">
            <image class="listItem_icon" src="https://document.dxznjy.com/manage/1718607597000"></image>
            <view class="itemInner">
              <text>教练管理</text>
            </view>
          </view>
          <!--  #endif  -->
          <view class="listItem" @click="goUrl('/qyWechat/downloadLink')">
            <image class="listItem_icon" src="https://document.dxznjy.com/automation/1721631752000"></image>
            <view class="itemInner">
              <text>鼎校会议下载</text>
            </view>
          </view>
          <!-- #ifdef MP-WEIXIN -->
          <view class="listItem" @click="depSync()">
            <image class="listItem_icon" src="https://document.dxznjy.com/dxSelect/icon_tongbu.png"></image>
            <view class="itemInner">
              <text>同步接单</text>
            </view>
          </view>
          <!-- #endif -->
          <!-- ProtocolList/ProtocolList -->
          <view class="listItem" @click="goUrl('/authen/ProtocolList/ProtocolList')">
            <image class="listItem_icon" src="https://document.dxznjy.com/dxSelect/2e905ead-6cb9-4cc8-a873-c66634dc2c3e.png"></image>
            <view class="itemInner">
              <text>协议及政策</text>
            </view>
          </view>
          <view class="listItem" @click="showKefuPhone()">
            <image class="listItem_icon" src="https://document.dxznjy.com/dxSelect/fourthEdition/home-lxkf.png"></image>
            <view class="itemInner">
              <text>联系我们</text>
            </view>
          </view>
          <view class="listItem" @click="back()">
            <image class="listItem_icon" src="https://document.dxznjy.com/dxSelect/f723f243-712c-4e0e-b62f-807174df8e3f.png"></image>
            <view class="itemInner">
              <text>注销账号</text>
            </view>
          </view>
          <!-- https://document.dxznjy.com/dxSelect/f723f243-712c-4e0e-b62f-807174df8e3f.png -->
          <!--      <view class="listItem" @click="goUrl('/qyWechat/bindGroup')">
              <image class="listItem_icon" src="https://document.dxznjy.com/Assistant/home_authen.png"></image>
              <view class="itemInner">
                <text>bindGroup</text>
              </view>
            </view> -->
          <!--      <view class="listItem" @click="goUrl('/qyWechat/changeGroup')">
              <image class="listItem_icon" src="https://document.dxznjy.com/Assistant/home_authen.png"></image>
              <view class="itemInner">
                <text>changeGroup</text>
              </view>
            </view> -->
          <!--      <view class="listItem" @click="goUrl('/qyWechat/notice')">
              <image class="listItem_icon" src="https://document.dxznjy.com/Assistant/home_authen.png"></image>
              <view class="itemInner">
                <text>notice</text>
              </view>
            </view> -->
          <!-- <view class="listItem" @click="goUrl('/pages/my/pronunciationSettings/myStudent?memberId='+ userlist.userCode)">
                <view class="listItem" @click="goUrl('/pronunciationSettings/myStudent?memberId='+ userlist.userCode)">
              <image class="listItem_icon" src="https://document.dxznjy.com/dxSelect/fourthEdition/icon_fysz.png"></image>
              <view class="itemInner">
                <text>发音设置</text>
              </view>
            </view> -->
          <button class="backbtn" type="primary" default @click="back">退出登录</button>
        </view>
      </scroll-view>
    </view>

    <!-- 自定义弹出框 -->
    <view>
      <uni-popup class="time_popup" ref="popup" type="bottom" @change="change" background-color="#fff">
        <view class="title">当前可用时间</view>
        <view class="close_icon" @click="close">
          <uni-icons type="clear" style="margin-right: 20rpx" size="30" color="#bfbfbf"></uni-icons>
        </view>

        <!-- 可用时间 -->
        <scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scroll="scroll">
          <!-- 有数据 -->
          <view class="choose" v-for="(item, index) in data" :key="index">
            <view class="choose_title">选择日期：</view>
            <view class="choose_date">
              <uni-data-select v-model="item.usableWeek" :clear="false" :localdata="datelist" @change="changeTime"></uni-data-select>
            </view>
            <view class="choose_time">
              <!-- <view class="uni-input" @click="shows = true">
                                  {{item.startTime === undefined ? '请选择' : item.startTime}}
                              </view>
                              <u-datetime-picker class="pikertime" :show="shows" v-model="item.startTime" mode="time"
                                  @confirm="confirm" :defaultIndex="index" @cancel="cancel">
                              </u-datetime-picker>
  
                              <view class="uni-input" @click="isShow = true">
                                  {{item.endTime === undefined ? '请选择' : item.endTime}}
                              </view>
                              <u-datetime-picker class="pikertime" :show="isShow" v-model="item.endTime" mode="time"
                                  @confirm="confirms" :defaultIndex="index" @cancel="cancels">
                              </u-datetime-picker> -->

              <picker mode="time" fields="day" :value="item.startTime" start="00:00" end="23:59" @change="bindPickerStart($event, item, index)" @cancel="cancel">
                <view class="uni-input">{{ item.startTime === undefined ? '请选择' : item.startTime }}</view>
              </picker>

              <picker mode="time" fields="day" :value="item.endTime" start="00:00" end="23:59" @change="bindPickerEnd($event, item, index)" @cancel="remove">
                <view class="uni-input">{{ item.endTime === undefined ? '请选择' : item.endTime }}</view>
              </picker>

              <uni-icons @click="add()" style="margin-left: 10rpx" type="plus-filled" size="30" color="#bfbfbf"></uni-icons>

              <uni-icons @click="del(index)" style="margin-right: 20rpx" type="minus-filled" size="30" color="#bfbfbf"></uni-icons>
            </view>
          </view>
          <!-- 有数据 -->
        </scroll-view>

        <!-- 按钮 -->
        <view class="select">
          <u-button class="backbtn" type="primary" size="small" @click="close">取消</u-button>
          <u-button class="backbtn" type="primary" size="small" @click="modify">更改</u-button>
        </view>
      </uni-popup>
    </view>
  </view>
</template>

<script>
  const { http } = require('@/utils/luch-request/index.js');
  import Util from '@/utils/util.js';
  import Config from '@/utils/config.js';
  import { mapGetters } from 'vuex';
  import imageCompress from '@/common/image.js';

  export default {
    data() {
      return {
        show: false,

        shows: false,
        isShow: false,
        userRole: uni.getStorageSync('isAppRoleValue'),
        timelist: {
          actualStart: '',
          actualEnd: ''
        },

        userlist: [], //个人信息
        avatarUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',

        // 星期选择
        usableWeek: '',
        datelist: [
          {
            value: 1,
            text: '星期一'
          },
          {
            value: 2,
            text: '星期二'
          },
          {
            value: 3,
            text: '星期三'
          },
          {
            value: 4,
            text: '星期四'
          },
          {
            value: 5,
            text: '星期五'
          },
          {
            value: 6,
            text: '星期六'
          },
          {
            value: 7,
            text: '星期天'
          }
        ],
        data: [
          {
            usableHourEnd: '',
            usableHourStart: '',
            usableWeek: ''
          }
        ],

        // 滚动条
        scrollTop: 0,
        old: {
          scrollTop: 0
        },

        value: 1,
        range: [
          {
            value: 1,
            text: '星期一'
          },
          {
            value: 2,
            text: '星期二'
          },
          {
            value: 3,
            text: '星期三'
          },
          {
            value: 4,
            text: '星期四'
          },
          {
            value: 5,
            text: '星期五'
          },
          {
            value: 6,
            text: '星期六'
          },
          {
            value: 7,
            text: '星期天'
          }
        ],

        defaultBuIndex: '',

        loginShow: false, // 是否显示登录
        isRefresh: false, //刷新控制
        useHeight: 0,
        userCode: '',
        scrollHeight: 0,

        withdrawalDetails: {}, // 提现明细
        bill: '',
        totalWithdrawal: '', // 累计提现

        remainder: '0.00', // 账期余额
        searchCode: '',
        searchType: '2'
      };
    },

    watch: {
      isRefresh(nowStatus, oldStatus) {
        this.getPersonalList();
        this.getPersonalTime();
      }
    },

    onLoad() {
      // uni.setStorageSync(
      //     'token',
      //     'eyJraWQiOiI0OTYwZWYwNi1lNDY2LTQ3MjYtOThlNi1mYjNkNGUwODk4NjgiLCJhbGciOiJSUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jkHQCuQji4KyCAcJHtiHWIRmpFrD3eEpQg2GvHdeM5VPnUDzdb-cwXYBdyfM89Ag0ci19z5nzGcR1VD5-W2jlmvgCfpuTbGk18NZ3Nim6_OIGTo_GpyaBUNuKSMortBbeHl6iWOOFqzBLXnOP4t4Ff3GcNkGkFUB2YIS9ie9QHUXrpwJc8OKlS5fHiyPCaWxdCpy4c608NhsTKj8UJ1Pjvel5EuAGNB5xxd-Dk1EeDr79yvbh3MYc22TEVY-KwS6hxCQ5YNLNaIo7o0UgQdkilvIUHJm6bXcKY-Qw1hQH-WsnRZsoopydFXKLk10XHJBEwl7jDJzunZ6rAWTMSAhjQ'
      // );
      // this.getPersonalList();
      this.getPersonalTime();
      // this.getMobile()
      // this.getQyUserInfo()
    },

    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 600;
          this.scrollHeight = h - 30;
          // if(this.screenHeight<=688&&this.screenHeight>619){
          // 	this.useHeight = h-790;
          // }else if (this.screenHeight<=619){
          // 	this.useHeight = h-770;
        }
      });
    },

    async onShow() {
      uni.removeStorage({
        key: 'logintokenReview'
      });

      try {
        await this.getPersonalList();
      } catch (error) {}
      let token = uni.getStorageSync('token');
      if (!token) {
        this.loginShow = true;
      } else {
        await this.getAccount();
        await this.getAccountingPeriodMoney();
        this.getUserAvatar();
        this.loginShow = false;
        // this.getAccountingPeriodMoney();
      }
    },

    computed: {
      // 这里的tabBarList就是数据源，直接使用传值
      ...mapGetters(['tabBarList'])
    },

    methods: {
      /**
       * 获取用户头像
       */
      getUserAvatar() {
        this.$http.get('/deliver/web/sysUser/getUserAvatar').then((res) => {
          console.log('我打的🚀🥶💩~ res.data.data', res.data.data);
          this.avatarUrl = res.data.data || 'https://document.dxznjy.com/dxSelect/home_avaUrl.png';
        });
      },

      showKefuPhone() {
        uni.showModal({
          title: '联系我们',
          content: '************'
        });
      },
      goBack() {
        plus.runtime.quit();
      },
      openPopup() {
        // this.$refs.addPopup.open();
        uni.navigateTo({
          url: '/qyWechat/setGroup'
        });
      },
      addSure() {
        let that = this;
        let mobile = uni.getStorageSync('tel');
        console.log(this.searchCode);
        if (!this.searchCode)
          return uni.showToast({
            title: '请输入学员编号',
            icon: 'none'
          });
        this.$http
          .get('/deliver/web/experience/getHistoryChatInfo', {
            studentCode: that.searchCode,
            type: that.searchType,
            // mobile: mobile
            mobile: '18374086485'
          })
          .then(({ data }) => {
            if (!data.data) {
              // that.searchCode = '';
              return that.$refs.uNotify.show({
                top: 60,
                type: 'error',
                color: '#000',
                bgColor: '#ffffff',
                message: '学员编号错误,请重新输入',
                duration: 1000 * 3,
                fontSize: 14
                // safeAreaInsetTop: true
              });
            } else {
              console.log(data.data);
              let courseList = data.data;
              let item = courseList[0];
              item.studentCode = that.searchCode;
              let sendData = encodeURIComponent(JSON.stringify(item));
              console.log(sendData);
              uni.navigateTo({
                url: `/qyWechat/judgeCode?sendData=${sendData}`
              });
            }
          })
          .catch((err) => {
            console.log(err, '===================');
          });
      },
      addCancel() {
        this.searchType = '2';
        this.searchCode = '';
        this.$refs.addPopup.close();
      },

      changeAvatar() {
        // #ifdef MP-WEIXIN
        // 企业微信环境
        if (typeof wx !== 'undefined' && wx.qy && wx.qy.getAvatar) {
          wx.qy.getAvatar({
            success: (res) => {
              var avatar = res.avatar;
              this.avatarUrl = avatar;
            },
            fail: (res) => {
              console.log(res.fail_reason);
            }
          });
        } else {
          // 普通微信小程序环境
          this.chooseAvatarFromAlbum();
        }
        // #endif

        // #ifdef APP-PLUS
        // App端（iOS和Android）
        this.showAvatarActionSheet();
        // #endif

        // #ifdef H5
        // H5端
        this.chooseAvatarFromAlbum();
        // #endif
      },

      // 显示头像选择操作菜单（App端）
      showAvatarActionSheet() {
        uni.showActionSheet({
          itemList: ['拍照', '从相册选择'],
          success: (res) => {
            if (res.tapIndex === 0) {
              // 拍照
              this.takePhoto();
            } else if (res.tapIndex === 1) {
              // 从相册选择
              this.chooseAvatarFromAlbum();
            }
          },
          fail: () => {
            console.log('取消选择');
          }
        });
      },

      // 拍照获取头像
      takePhoto() {
        uni.chooseImage({
          count: 1,
          sizeType: ['compressed'], // 压缩图
          sourceType: ['camera'], // 只允许拍照
          success: (res) => {
            const tempFilePath = res.tempFilePaths[0];
            this.processAvatar(tempFilePath);
          },
          fail: (err) => {
            console.log('拍照失败:', err);
            uni.showToast({
              title: '拍照失败',
              icon: 'none'
            });
          }
        });
      },

      // 从相册选择头像
      chooseAvatarFromAlbum() {
        uni.chooseImage({
          count: 1,
          sizeType: ['compressed'], // 压缩图
          sourceType: ['album'], // 只允许从相册选择
          success: (res) => {
            const tempFilePath = res.tempFilePaths[0];
            console.log('选择的头像路径:', tempFilePath);
            this.processAvatar(tempFilePath);
          },
          fail: (err) => {
            console.log('选择图片失败:', err);
            uni.showToast({
              title: '选择图片失败',
              icon: 'none'
            });
          }
        });
      },

      // 处理选择的头像
      async processAvatar(tempFilePath) {
        try {
          // 显示加载提示
          uni.showLoading({
            title: '处理中...'
          });

          // 压缩图片（如果需要）
          let processedPath = tempFilePath;
          console.log('🚀🥶💩~ processedPath', processedPath);
          // #ifdef APP-PLUS
          // App端使用图片压缩
          try {
            const compressedImages = await new Promise((resolve) => {
              imageCompress.compress([tempFilePath], (result) => {
                resolve(result);
              });
            });
            console.log('🚀🥶💩~ compressedImages', compressedImages);
            if (compressedImages && compressedImages.length > 0) {
              processedPath = compressedImages[0];
              console.log('1🚀🥶💩~ processedPath ', processedPath);
            }
          } catch (compressError) {
            console.log('图片压缩失败，使用原图:', compressError);
            // 压缩失败时使用原图
          }
          // #endif

          // 上传头像到服务器
          await this.uploadAvatar(processedPath);
        } catch (error) {
          console.error('处理头像失败:', error);
          uni.showToast({
            title: '处理失败',
            icon: 'none'
          });
        } finally {
          uni.hideLoading();
        }
      },

      // 上传头像到服务器
      async uploadAvatar(filePath) {
        console.log('上传头像到服务器🚀🥶💩~ filePath', filePath);
        try {
          let that = this;
          // 先更新本地显示
          // this.avatarUrl = filePath;
          // 上传到服务器
          await uni.uploadFile({
            url: `${Config.DXUploadHost}/zx/common/uploadFile`, // 根据实际API调整
            filePath: filePath,
            name: 'file',
            header: {
              'x-www-iap-assertion': uni.getStorageSync('token')
            },
            success(res) {
              console.log('上传成功的res🚀🥶💩~ res', res);
              // let url={"data":"{\"status\":1,\"message\":\"操作成功\",\"data\":{\"fileSize\":\"283.19KB\",\"fileUrl\":\"https://document.dxznjy.com/course/f535a43ec0cb4772a2f56b7de72b8bba.JPG\"}}"
              try {
                // let str = JSON.parse(res).data
                // console.log('🚀🥶💩~ str', str)
                // let infoObj = JSON.parse(str)
                // console.log('🚀🥶💩~ infoObj', infoObj)
                // console.log('🚀🥶💩~ infoObj.code', infoObj.code)
                const data = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
                console.log('📋 解析后的响应数据:', data);

                const url = data?.data?.fileUrl;
                console.log('🔗 提取的文件URL:', url);
                if (url) {
                  console.log('✅ 文件上传成功，URL:', url);
                  // 可以在这里处理上传成功后的逻辑
                  that.avatarUrl = url;
                  uni.$http.get(`/deliver/web/sysUser/updateUserAvatar?avatarUrl=${that.avatarUrl}`);
                } else {
                  const errorMsg = data?.message || '上传失败';
                  console.error('❌ 上传失败，错误信息:', errorMsg);
                  uni.showToast({ title: errorMsg, icon: 'none' });
                }
                // if (infoObj.code == 50002) {
                //   uni.showToast({
                //     title: infoObj.message,
                //     icon: 'none'
                //   });
                //   retrun;
                // }
                // const avatarUrl = JSON.parse(res.data)?.data?.fileUrl;
                // console.log('🚀🥶💩~头像 avatarUrl', avatarUrl);
                // 先更新本地显示
                // 恢复原头像
                // that.getUserAvatar();
              } catch (error) {
                console.error('上传头像失败:', error);
                uni.showToast({
                  title: '上传失败',
                  icon: 'none'
                });

                // 恢复原头像
                that.getUserAvatar();
              }
            },
            fail(err) {
              console.log('🚀 ~ fail ~ err:', err);
              console.log(Config.DXUploadHost, 'Config.DXUploadHost');
            }
          });
        } catch (error) {
          console.error('上传头像失败:', error);
          uni.showToast({
            title: '上传失败',
            icon: 'none'
          });
          // 恢复原头像
          this.getUserAvatar(); // 重新获取用户信息
        }
      },
      setRefresh(type) {
        this.isRefresh = type;
      },
      scroll: function (e) {
        console.log(e);
        this.old.scrollTop = e.detail.scrollTop;
      },

      gologin() {
        let islogin = uni.getStorageSync('isLogin');
        uni.setStorageSync('isLogin', false);
        uni.removeStorageSync('logintoken');
        let token = uni.getStorageSync('token');
        if (islogin == '' || islogin == false || !token) {
          this.$tool.toLoginPage(2);
          uni.setStorageSync('isLogin', 'islogin');
        } else {
        }
      },

      // 请假调整时间
      changeTime(e) {
        console.log(e);
        // let time = e.value;
        // this.times.starttimes = dayjs(time).format('YYYY-MM-DD HH:mm')
      },

      // picker的change事件
      bindPickerStart(e, item, index) {
        item.startTime = e.detail.value;
        this.$forceUpdate();
      },

      bindPickerEnd(e, item, index) {
        item.endTime = e.detail.value;
        this.$forceUpdate();
      },

      // 时间选择取消
      cancel() {
        console.log('取消了');
      },

      remove() {
        console.log('取消了');
      },

      // 添加对象
      add() {
        this.data.push({
          usableHourEnd: '',
          usableHourStart: '',
          usableWeek: ''
        });
      },

      // 删除对象
      del(index) {
        if (this.data.length > 0) {
          if (this.data.length === 1) {
            this.data = [
              {
                usableHourEnd: '',
                usableHourStart: '',
                usableWeek: ''
              }
            ];
          } else {
            this.data.splice(index, 1);
          }
        }
      },

      change(e) {
        this.show = e.show;
      },
      depSync() {
        let mobile = uni.getStorageSync('tel');
        this.$scrmHttp
          .post('/scrm/sync/user/needSync', {
            mobile
          })
          .then((res) => {
            if (res.data.success) {
              uni.showModal({
                title: '温馨提示',
                content: '同步成功',
                showCancel: false
              });
            }
          });
      },
      goUrl(url) {
        let token = uni.getStorageSync('token');
        if (token) {
          uni.navigateTo({
            url: url
          });
        } else {
          this.$tool.toLoginPage(2);
        }
      },

      // 可用时间取消
      close() {
        this.$refs.popup.close();
        this.getPersonalTime();
      },

      // 可用时间更改按钮
      modify() {
        this.getamendTime();
      },

      // 选择日期
      changeTime(e) {
        console.log('e:', e);
      },

      // 选择时间(删除)
      start(e) {
        console.log('e:', e);
      },

      // 信息跳转
      skips() {
        let token = uni.getStorageSync('token');
        if (token) {
          uni.navigateTo({
            url: '/news/news'
          });
        } else {
          this.$tool.toLoginPage(2);
        }
      },

      // 退出登录
      back() {
        uni.removeStorageSync('token');
        uni.removeStorageSync('scrm_token');
        uni.setStorageSync('userRole', 'notLogin');
        uni.setStorageSync('isLogin', false);
        this.$store.commit('setType', 'notLogin');
        // #ifdef APP-PLUS
        uni.$exitLogin();
        plus.runtime.quit();
        // #endif

        // #ifdef MP-WEIXIN
        uni.reLaunch({
          url: '/pages/index/index'
        });
        // #endif
      },

      // 获取个人信息(账号，姓名)
      async getPersonalList() {
        let { data } = await uni.$http.get('/deliver/app/teacher/info');
        this.userlist = data.data || {};

        // 更新头像URL（如果服务器返回了头像信息）
        if (this.userlist.avatarUrl) {
          this.avatarUrl = this.userlist.avatarUrl;
        }

        uni.getStorageSync('teacherName', this.userlist.name);
        uni.getStorageSync('teacherCode', this.userlist.teacherCode);
        uni.getStorageSync('merchantName', this.userlist.merchantName);
        uni.getStorageSync('merchantCode', this.userlist.merchantCode);
      },

      // 获取教练师可用时间
      async getPersonalTime() {
        let { data } = await uni.$http.get('/deliver/app/teacher/getAvailableWeek');
        let d = data.data;
        if (d != null && d.length > 0) {
          this.data = d;
        }
      },

      // 修改教练师可用时间
      async getamendTime() {
        let nowData = '"' + JSON.stringify(this.data).replace(/"/g, '\\"') + '"';
        let res = await uni.$http.post(`/deliver/app/teacher/updateAvailableWeek`, nowData);
        if (res.data.success) {
          this.$refs.popup.close();
        }
      },

      // 获取俱乐部金额
      async getAccount() {
        let that = this;
        let res = await http.get('/mps/account/list/login/code/byCode', {
          userCode: that.userlist.userCode
        });
        if (res.data.success) {
          for (let i = 0; i < res.data.data.length; i++) {
            if (res.data.data[i].userCode == this.userlist.userCode) {
              that.withdrawalDetails = res.data.data[i];
            }
          }
          if (Object.keys(this.withdrawalDetails).length === 0) {
            console.log('111111111111111111111');
          } else {
            this.withdrawalDetails.totalMoney = Util.Fen2Yuan(this.withdrawalDetails.totalMoney || 0); // 总收益
            this.withdrawalDetails.availableCashAmount = Util.Fen2Yuan(this.withdrawalDetails.availableCashAmount || 0); // 可提现
            this.withdrawalDetails.paymentIn = Util.Fen2Yuan(this.withdrawalDetails.paymentIn || 0); // 已结算
            this.getWithdrawal();
          }
        }
      },

      // 获取累计提现金额
      async getWithdrawal() {
        let that = this;
        let res = await http.get('/deliver/app/teacher/getTeacherWithdrawMoney', {
          userCode: that.userlist.userCode
        });
        if (res.data.success) {
          console.log(res);
          that.totalWithdrawal = Number(res.data.data).toFixed(2);
          // that.remainder = Number(Number(that.withdrawalDetails.totalMoney) - Number(that.totalWithdrawal)).toFixed(2);
        }
      },
      async getAccountingPeriodMoney() {
        let that = this;
        let res = await http.get('/deliver/app/teacher/getAccountingPeriodMoney', {
          userCode: that.userlist.userCode
        });
        console.log(res.data.data, 'ooooooooooooooooooo');
        if (res.data.success) {
          that.remainder = Number(res.data.data);
        }
      }
    }
  };
</script>

<style>
  page {
    overflow: hidden;
  }
</style>
<style lang="scss" scoped>
  // .scroll-Y {
  // 	height: 1100rpx;
  // }

  // .scroll-view_H {
  // 	white-space: nowrap;
  // 	width: 100%;
  // }

  // .scroll-view-item {
  // 	height: 300rpx;
  // 	line-height: 300rpx;
  // 	text-align: center;
  // 	font-size: 36rpx;
  // }

  // .scroll-view-item_H {
  // 	display: inline-block;
  // 	width: 100%;
  // 	height: 300rpx;
  // 	line-height: 300rpx;
  // 	text-align: center;
  // 	font-size: 36rpx;
  // }

  .nav-title {
    display: flex;
    width: 100%;
    padding-top: 100rpx;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .backImg {
    position: absolute;
    top: 100rpx;
    left: 5%;
    transform: translate(-50%, -50%);
    z-index: 999;
  }

  .backIcon {
    position: absolute;
    left: 30upx;
  }

  .status_bar {
    position: fixed;
    display: inline-flex;
    text-align: center;
    font-size: 32upx;
    color: #000000;
  }

  .my_list {
    position: absolute;
    left: 30rpx;
    top: 480rpx;
    width: 690upx;
    min-height: 60vh;
    margin: 80upx auto 0 auto;
    background-color: #ffffff;
    border-radius: 15upx;

    .listItem {
      height: 100upx;
      display: flex;
      align-items: center;
      margin-left: 30rpx;
      margin-right: 30rpx;
      border-bottom: 1upx solid #eee;

      .listItem_icon {
        width: 40upx;
        height: 40upx;
        margin-right: 30upx;
      }
    }
  }

  .itemInner {
    width: 590upx;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    font-size: 28upx;
    height: 100%;
    align-items: center;
  }

  .listItem:last-child .itemInner {
    border: none;
  }

  .backbtn {
    // position: absolute;
    // bottom: 30rpx;
    color: #333;
    width: 630rpx;
    height: 90rpx;
    font-size: 30rpx;
    margin-top: 30rpx;
    margin-left: 30rpx;
    line-height: 90rpx;
    border-radius: 12rpx;
    background-color: #f2f2f2;
  }

  /deep/ .time_popup {
    position: relative;
    // padding: 30rpx 40rpx !important;

    .title {
      margin-top: 20rpx;
      font-weight: 700;
      margin-left: 260rpx;
      font-size: 40rpx !important;
    }

    .choose {
      display: flex;
      height: 90rpx;
      width: 100%;

      .choose_title {
        line-height: 150rpx;
        margin-left: 20rpx;
        font-size: 26rpx;
        padding: 0;
        width: 100%;
      }

      .choose_date {
        margin-top: 40rpx;
        width: 170rpx;
        width: 100%;
      }

      .choose_time {
        display: flex;
        padding: 0;
        margin-top: 40rpx;
        width: 100%;
      }

      /deep/ .data-v-52d4ddd1 {
        z-index: 999 !important;
        background-color: rgba(0, 0, 0, 0) !important;
      }

      .uni-input {
        height: 67rpx;
        width: 140rpx;
        text-align: center;
        line-height: 67rpx;
        font-size: 28rpx;
        color: #333;
        border: 1px solid #e5e5e5;
        overflow: visible;
        border-radius: 10rpx;
        transform: scale(0.995);
        /* 解决ios上圆角失效 */
      }
    }

    .close_icon {
      position: absolute;
      top: 0;
      right: 0;
    }

    .select {
      display: flex;
      padding: 0 100rpx;
    }

    .u-button {
      background-color: #0398ef;
      height: 40rpx;
      padding: 30rpx 0rpx;
      margin-bottom: 10rpx;
      font-size: 26rpx;
      margin-left: 70rpx;
      margin-right: 70rpx;
    }
  }

  .hear_img {
    width: 100%;
    height: 560rpx;
    position: fixed;
    z-index: 0;
  }

  .container {
    position: relative;
    // width: 100%;
    // height: 662upx;
    // position: fixed;
    // box-sizing: border-box;
    // background: url("/static/images/home_hear.png") 100% 100% no-repeat;
    // background-size: 100% 100%;
    // z-index: -1;

    .tidings {
      position: absolute;
      top: 195rpx;
      right: 55rpx;
      width: 55rpx;
      min-width: 50rpx;
      min-height: 50rpx;
      height: 55rpx;
      line-height: 55rpx;
      border-radius: 50%;
      text-align: center;
      background-color: rgba(255, 255, 255, 0.3);
    }

    .box {
      position: absolute;
      right: -5rpx;
    }

    /deep/ .data-v-26a60cd2 {
      padding: 0;
    }

    // 个人信息

    .personalBox {
      position: absolute;
      top: 90rpx;
      width: 100%;
      height: 140upx;
      display: flex;
      align-items: center;
      padding: 0 50upx;
      box-sizing: border-box;
      color: #ffffff;
      margin: 55upx auto;
    }

    .userImg {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      margin-right: 36upx;
      padding: 0;
    }
  }

  .money {
    position: absolute;
    top: 370rpx;
    left: 100rpx;
    width: 73%;
  }

  .mobile_style {
    padding: 0 8rpx;
    height: 38rpx;
    color: #666666;
    font-size: 28rpx;
    line-height: 38rpx;
    text-align: center;
    background: #eeeeee;
    border-radius: 8rpx;
  }

  .merchantName-style {
    text-align: center;
    padding-left: 12rpx;
    padding-right: 12rpx;
    height: 38rpx;
    background: linear-gradient(130deg, #f5ebd6 0%, #dec288 100%, #dec288 100%);
    border-radius: 6rpx;
    color: #886a34;
    font-size: 26rpx;
  }

  .details {
    width: 140rpx;
    height: 46rpx;
    color: #16523e;
    line-height: 46rpx;
    text-align: center;
    border-radius: 30rpx;
    background-image: linear-gradient(to right, #ebf8f2, #c2cfc8);
  }

  .dialogBG {
    margin: 200rpx 30rpx 30rpx 30rpx;
    background-color: #fff;
    border-radius: 14rpx;
  }

  .dialog-all {
    width: 600rpx;
    position: relative;
  }

  .dialog-all image {
    width: 100%;
    height: 100%;
  }

  .input-border {
    height: 80rpx;
    border-radius: 14rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.2);
  }

  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .flex-center1 {
    display: flex;
    align-items: center;
    // justify-content: center;
    padding-left: 10rpx;
  }

  .common-sure-btn {
    width: 250rpx;
    height: 80rpx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 40rpx;
    font-size: 30rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 40rpx;
    transform: scale(0.995);
    /* 解决ios上圆角失效 */
  }

  .common-cancel-btn {
    width: 250rpx;
    height: 80rpx;
    border-radius: 40rpx;
    border: 1rpx solid #2e896f;
    font-size: 30rpx;
    color: #2e896f;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 40rpx;
    transform: scale(0.995);
    /* 解决ios上圆角失效 */
  }

  .search-dialog-student-input {
    color: #666666;
    margin-left: 20rpx;
    width: 440rpx;
    height: 40rpx;
    font-size: 28rpx;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 100rpx;
    z-index: -1;
  }

  ::v-deep .u-radio {
    margin-right: 80rpx;
  }
</style>
