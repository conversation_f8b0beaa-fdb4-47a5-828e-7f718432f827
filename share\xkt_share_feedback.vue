<template>
  <view style="height: 1600prx; background-color: #2e896f">
    <view>
      <image :src="path" mode="widthFix" @longpress="longPress()" style="width: 100%"></image>
      <Painter isCanvasToTempFilePath ref="painter" @success="path = $event" custom-style="position: fixed; left: 200%" css="width: 750rpx; padding-bottom: 40rpx">
        <PainterImage src="https://document.dxznjy.com/app/images/zhujiaoduan/feedback_bg.png" css="position: absolute;top:-50rpx;object-fit: contain;width: 100%;"></PainterImage>
        <PainterView css="position: absolute;top:120rpx;z-index:2;width:100%;text-align:center;color:#FFFFFF;font-size:34rpx;">
          <PainterText text="学习反馈" />
        </PainterView>
        <PainterView css="position: relative;margin-top: 250rpx; padding: 32rpx; box-sizing: border-box; background: #fff;border:30rpx solid #2e896f;">
          <!-- 学习反馈  -->
          <PainterView v-if="isStudy && trialclass">
            <PainterText :text="'日期：' + triallist.dateTime" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'姓名：' + triallist.studentName" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'课程类型：' + triallist.curriculumName" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'年级：' + triallist.gradeName" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'学员编号：' + triallist.studentCode" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'上课用时：' + triallist.studyHour + '小时'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />

            <PainterText
              v-if="XKTCurriculumCodeArr.includes(triallist.curriculumCode)"
              :text="'授课视频：' + triallist.xktStatisticsDto.xktGradeName + '/' + triallist.xktStatisticsDto.xktCourseName + '/' + triallist.xktStatisticsDto.xktVideoName"
              css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
            />
            <PainterView css="width:100%;height:100%" v-if="XKTCurriculumCodeArr.includes(triallist.curriculumCode)">
              <PainterImage :src="triallist.xktFeedbackTable.imageUrl" css="object-fit: contain;width: 100%;"></PainterImage>
            </PainterView>
            <PainterText
              v-if="CurriculumCodeArr.includes(triallist.curriculumCode)"
              text="教练评语："
              css="font-size: 30rpx;display: block;margin-bottom: 30rpx;line-height: 1.8em"
            />
            <PainterView
              v-if="CurriculumCodeArr.includes(triallist.curriculumCode)"
              css="padding: 30rpx;box-sizing: border-box;background: rgba(153, 153, 153, 0.08);border-radius: 8rpx;width: 630rpx;min-height:481rpx;"
            >
              <PainterText :text="feedback" css="font-size: 30rpx;display: block;line-height: 1.8em" />
            </PainterView>
          </PainterView>
          <PainterView v-else>
            <PainterText :text="'日期：' + backlist.dateTime" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'姓名：' + backlist.studentName" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'课程类型：' + backlist.curriculumName" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'年级：' + backlist.gradeName" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'学员编号：' + backlist.studentCode" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'上课用时：' + backlist.studyHour + '小时'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'已购' + backlist.curriculumName + '学时：' + backlist.totalCourseHours + '小时'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <PainterText :text="'剩余' + backlist.curriculumName + '学时：' + backlist.leaveCourseHours + '小时'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />

            <PainterText
              v-if="XKTCurriculumCodeArr.includes(backlist.curriculumCode)"
              :text="'授课视频：' + backlist.xktStatisticsDto.xktGradeName + '/' + backlist.xktStatisticsDto.xktCourseName + '/' + backlist.xktStatisticsDto.xktVideoName"
              css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
            />
            <PainterView css="width:100%;height:100%" v-if="XKTCurriculumCodeArr.includes(backlist.curriculumCode)">
              <PainterImage :src="backlist.xktFeedbackTable.imageUrl" css="object-fit: contain;width: 100%;"></PainterImage>
            </PainterView>
            <PainterText
              v-if="CurriculumCodeArr.includes(backlist.curriculumCode)"
              text="教练评语："
              css="font-size: 30rpx;display: block;margin-bottom: 30rpx;line-height: 1.8em"
            />

            <PainterView
              v-if="CurriculumCodeArr.includes(backlist.curriculumCode)"
              css="padding: 30rpx;box-sizing: border-box;background: rgba(153, 153, 153, 0.08);border-radius: 8rpx;width: 630rpx;min-height:481rpx;"
            >
              <PainterText :text="feedback" css="font-size: 30rpx;display: block;line-height: 1.8em" />
            </PainterView>
          </PainterView>
        </PainterView>
      </Painter>
    </view>
  </view>
</template>

<script>
  import PainterText from '@/share/components/lime-painter/components/l-painter-text/l-painter-text.vue';
  import PainterImage from '@/share/components/lime-painter/components/l-painter-image/l-painter-image.vue';
  import PainterView from '@/share/components/lime-painter/components/l-painter-view/l-painter-view.vue';
  import Painter from '@/share/components/lime-painter/components/l-painter/l-painter.vue';
  import Config from '@/utils/config.js';
  export default {
    components: {
      PainterText,
      Painter,
      PainterImage,
      PainterView
    },
    data() {
      return {
        path: '', //生成海报
        backlist: '', // 获取反馈详情
        triallist: '', // 试课反馈详情
        //反馈详情列表
        trialclass: false, // 是否是试课反馈
        isStudy: false, // 学习还是复习

        feedback: '', // 弹出层文本框输入的内容
        Intention: '', // 学习意愿
        timelist: {
          actualStart: '',
          actualEnd: ''
        },
        XKTCurriculumCodeArr: Config.XKTCurriculumCodeArr, //学考通相关课程大类
        CurriculumCodeArr: Config.CurriculumCodeArr,
        tableList: [
          {
            title: '课堂实时监测',
            itemList: [
              {
                litterTitle: '学习准备',
                radioGroup: [
                  {
                    key: '1.预习任务完成',
                    value: ''
                  },
                  {
                    key: '2.进门测试题目正确率',
                    value: ''
                  }
                ]
              },
              {
                litterTitle: '注意力集中度',
                radioGroup: [
                  {
                    key: '1.眼神跟随教师/屏幕',
                    value: ''
                  },
                  {
                    key: '2.无关操作<=2次/节课',
                    value: ''
                  }
                ]
              },
              {
                litterTitle: '课堂参与度',
                radioGroup: [
                  {
                    key: '1.主动回答问题>=1次/节课',
                    value: ''
                  },
                  {
                    key: '2.互动次数和质量',
                    value: ''
                  }
                ]
              }
            ]
          },
          {
            title: '教学效果观察',
            itemList: [
              {
                litterTitle: '知识掌握',
                radioGroup: [
                  {
                    key: '1.课后留题正确率>=70%',
                    value: ''
                  },
                  {
                    key: '2.课后错题讲解+知识点回顾质量',
                    value: ''
                  }
                ]
              },
              {
                litterTitle: '精彩时刻',
                radioGroup: [
                  {
                    key: '1.听课状态',
                    value: ''
                  }
                ]
              },
              {
                litterTitle: '情绪与态度',
                radioGroup: [
                  {
                    key: '1.学习积极性',
                    value: ''
                  },
                  {
                    key: '2.是否有厌学情绪，抵触心理',
                    value: ''
                  }
                ]
              }
            ]
          }
        ]
      };
    },
    onLoad(e) {
      let sendData = JSON.parse(decodeURIComponent(e.sendData));
      console.log('eee', e, sendData);
      if (sendData != null && sendData != undefined) {
        this.trialclass = sendData.trialclass;
        this.isStudy = sendData.isStudy;
        this.triallist = sendData.detailsData;
        this.backlist = sendData.detailsData;
        if (this.backlist != null && this.backlist != undefined) {
          this.Intention = this.backlist.studyIntention;
          this.feedback = this.backlist.feedback;
          this.timelist.actualStart = this.backlist.actualStart;
          this.timelist.actualEnd = this.backlist.actualEnd;
        }
      }
    },
    methods: {
      //长按
      longPress() {
        let that = this;
        uni.showModal({
          content: '保存图片',
          success: (res) => {
            if (res.confirm) {
              that.$refs.painter.canvasToTempFilePathSync({
                fileType: 'jpg',
                pathType: 'url',
                quality: 1,
                success: (res) => {
                  console.log(res);
                  that.path = res.tempFilePath;
                  that.saveLocal();
                },
                fail: (err) => {
                  console.log(err);
                  uni.showModal({
                    title: '提示',
                    content: '生成海报失败,请重试',
                    showCancel: false
                  });
                }
              });
            } else {
              uni.showToast({
                title: '已取消！',
                icon: 'none'
              });
            }
          }
        });
      },

      //保存本地
      saveLocal() {
        // #ifdef APP-PLUS
        uni.saveImageToPhotosAlbum({
          filePath: this.path,
          success: (res) => {
            uni.showToast({
              icon: 'none',
              title: '保存成功'
            });
            console.log(res);
          },
          fail(err) {
            uni.showToast({
              icon: 'none',
              title: '保存失败'
            });
          }
        });
        // #endif
        // #ifdef MP-WEIXIN
        uni.authorize({
          scope: 'scope.writePhotosAlbum',
          success: () => {
            uni.saveImageToPhotosAlbum({
              filePath: this.path,
              success: (res) => {
                uni.showToast({
                  icon: 'none',
                  title: '保存成功'
                });
                console.log(res);
                //分享
                // wx.showShareImageMenu({
                //     path: res.path,
                //     success(msg) {
                //         console.log(msg);
                //     },
                //     fail(err) {
                //         console.log("11111");
                //         console.log(err);
                //     }
                // })
              },
              fail(err) {
                uni.showToast({
                  icon: 'none',
                  title: '保存失败'
                });
              }
            });
          },
          fail() {
            uni.showModal({
              title: '保存失败',
              content: '您没有授权，无法保存到相册',
              showCancel: false
            });
          }
        });
        // #endif
      },

      Back() {
        uni.navigateBack();
      }
    }
  };
</script>
