{
	"easycom": {
		"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [
		//pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/study_feedback",
			"style": {
				"navigationBarTitleText": "",
				"backgroundColor": "#F3F8FC"
			}
		},
		{
			"path": "pages/index/revise_feedback",
			"style": {
				"navigationBarTitleText": "",
				"backgroundColor": "#F3F8FC"
			}
		},
		{
			"path": "pages/index/web"
		},
		{
			"path": "pages/index/showImage",
			"style": {
				"navigationBarTitleText": "学情报告",
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/my/my",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		// , {
		//   "path": "pages/news/news",
		//   "style": {
		//     "navigationBarTitleText": "消息",
		//     "enablePullDownRefresh": false,
		//     "navigationStyle": "default"
		//   }
		// }
		{
			"path": "pages/my/wallet/index",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/my/wallet/billDetails",
			"style": {
				"navigationBarTitleText": "账单详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/my/wallet/wallet",
			"style": {
				"navigationBarTitleText": "提现",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/student/index",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/review/index",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/wordMore",
			"style": {
				"navigationBarBackgroundColor": "#F3F8FC",
				"navigationBarTitleText": "词库",
				"enablePullDownRefresh": false,
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/my/pronunciationSettings/myStudent",
			"style": {
				"navigationBarTitleText": "我的发音",
				"enablePullDownRefresh": false,
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/my/pronunciationSettings/index",
			"style": {
				"navigationBarTitleText": "发音设置",
				"enablePullDownRefresh": false,
				"navigationStyle": "default"
			}
		}
	],
	"easycom": {
		"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [
		//pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/study_feedback",
			"style": {
				"navigationBarTitleText": "",
				"backgroundColor": "#F3F8FC"
			}
		},
		{
			"path": "pages/index/revise_feedback",
			"style": {
				"navigationBarTitleText": "",
				"backgroundColor": "#F3F8FC"
			}
		},
		{
			"path": "pages/index/web"
		},
		{
			"path": "pages/index/showImage",
			"style": {
				"navigationBarTitleText": "学情报告",
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/my/my",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		// , {
		//   "path": "pages/news/news",
		//   "style": {
		//     "navigationBarTitleText": "消息",
		//     "enablePullDownRefresh": false,
		//     "navigationStyle": "default"
		//   }
		// }
		{
			"path": "pages/my/wallet/index",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/my/wallet/billDetails",
			"style": {
				"navigationBarTitleText": "账单详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/my/agreement/agreement",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/my/wallet/wallet",
			"style": {
				"navigationBarTitleText": "提现",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/student/index",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/review/index",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/wordMore",
			"style": {
				"navigationBarBackgroundColor": "#F3F8FC",
				"navigationBarTitleText": "词库",
				"enablePullDownRefresh": false,
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/my/pronunciationSettings/myStudent",
			"style": {
				"navigationBarTitleText": "我的发音",
				"enablePullDownRefresh": false,
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/my/pronunciationSettings/index",
			"style": {
				"navigationBarTitleText": "发音设置",
				"enablePullDownRefresh": false,
				"navigationStyle": "default"
			}
		}
	],

	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "",
		"navigationBarBackgroundColor": "#fff",
		"backgroundColor": "#F8F8F8",
		"navigationStyle": "custom"
	},
	"tabBar": {
		"color": "#BDBDBD",
		"selectedColor": "#EA6031",
		"backgroundColor": "#ffffff",
		"borderStyle": "white",
		"list": [{
				"pagePath": "pages/index/index",
				"text": "学习课程表",
				"iconPath": "/static/images/tab_home.png",
				"selectedIconPath": "/static/images/tab_home_active.png",
				"color": "#BDBDBD"
			},
			{
				"pagePath": "pages/review/index",
				"text": "复习课程表",
				"iconPath": "/static/images/tab_review.png",
				"selectedIconPath": "/static/images/tab_review_active.png",
				"color": "#BDBDBD"
			},
			{
				"pagePath": "pages/student/index",
				"text": "学员管理",
				"iconPath": "/static/images/tab_student.png",
				"selectedIconPath": "/static/images/tab_student_active.png",
				"color": "#BDBDBD"
			},
			{
				"pagePath": "pages/my/my",
				"text": "我的",
				"iconPath": "/static/images/tab_user.png",
				"selectedIconPath": "/static/images/tab_user_active.png",
				"color": "#BDBDBD"
			}
		]
	},
	// 配置分包
	"subPackages": [{
			"root": "Coursedetails",
			"name": "Coursedetails",
			"plugins": {
				"polyv-player": {
					"version": "1.0.0",
					"provider": "wx4a350a258a6f7876"
				}
			},
			"pages": [{
				"path": "pinYin/preschoolVideo",
				"style": {
					"navigationBarTitleText": "学前视频",
					"enablePullDownRefresh": false,
					"navigationStyle": "default",
					"mp-weixin": {
						"usingComponents": {
							"polyv-player": "plugin://polyv-player/player"
						}
					}
				}
			}]
		},
		{
			"root": "vocabulary",
			"name": "vocabulary",
			"pages": [{
					"path": "report",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "vocabulary",
					"style": {
						"navigationBarTitleText": "词汇量报告",
						"navigationStyle": "default",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "dictation",
					"style": {
						"navigationBarTitleText": "听写能力检测报告",
						"navigationStyle": "default",
						"enablePullDownRefresh": false,
						"navigationBarBackgroundColor": "#f3f8fc"
					}
				},
				{
					"path": "dictationSingleReport",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "studyPrint",
			"name": "studyPrint",
			"pages": [{
					"path": "studyContentPrint",
					"style": {
						"navigationBarTitleText": "学习内容打印",
						"navigationStyle": "default",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "studyPrint",
					"style": {
						"navigationBarTitleText": "学习内容打印",
						"navigationStyle": "default",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "interestModule",
			"name": "interestModule",
			"pages": [{
					"path": "funReview",
					"style": {
						"navigationStyle": "custom",
						"enablePullDownRefresh": true,
						"app-plus": {
							"titleNView": false
						}
					}
				},
				{
					"path": "interestingLlk",
					"style": {
						"navigationStyle": "custom",
						"enablePullDownRefresh": true,
						"app-plus": {
							"titleNView": false
						}
					}
				},
				{
					"path": "interestingPpl",
					"style": {
						"navigationStyle": "custom",
						"enablePullDownRefresh": true,
						"app-plus": {
							"titleNView": false
						}
					}
				},
				{
					"path": "interestingSccg",
					"style": {
						"navigationStyle": "custom",
						"enablePullDownRefresh": true,
						"app-plus": {
							"titleNView": false
						}
					}
				},
				{
					"path": "interestingTyby",
					"style": {
						"navigationStyle": "custom",
						"enablePullDownRefresh": true,
						"app-plus": {
							"titleNView": false
						}
					}
				}
			]
		},
		{
			"root": "antiForgetting",
			"name": "antiForgetting",
			"pages": [{
					"path": "allWords",
					"style": {
						"navigationBarTitleText": "今日复习",
						"enablePullDownRefresh": false,
						"navigationStyle": "default"
					}
				},
				{
					"path": "history",
					"style": {
						"onReachBottomDistance": 100,
						"navigationBarTitleText": "往期复习",
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#FFFFFF",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "grammarHistory",
					"style": {
						"onReachBottomDistance": 100,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "historyReviewReport",
					"style": {
						"navigationStyle": "custom"
					}
				},
				{
					"path": "handout",
					"style": {
						"navigationStyle": "custom"
					}
				},
				{
					"path": "hisGraReviewReport",
					"style": {
						"navigationBarTitleText": "复习报告",
						"navigationStyle": "default"
					}
				},
				{
					"path": "index",
					"style": {
						"navigationBarTitleText": "复习",
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#FFFFFF",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "list",
					"style": {
						"onReachBottomDistance": 100,
						"navigationBarTitleText": "复习单词",
						"navigationStyle": "custom",
						"navigationBarBackgroundColor": "#FFFFFF",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "grammarList",
					"style": {
						"onReachBottomDistance": 100,
						"navigationBarTitleText": "复习语法",
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#FFFFFF",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "grammarPreview",
					"style": {
						"navigationBarTitleText": "语法预览",
						"enablePullDownRefresh": false,
						"navigationBarBackgroundColor": "#f3f8fc",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "reviewReport",
					"style": {
						"navigationStyle": "custom"
					}
				},
				{
					"path": "wordPreview",
					"style": {
						"navigationBarTitleText": "单词预览",
						"enablePullDownRefresh": false,
						"navigationBarBackgroundColor": "#f3f8fc",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "aiReviewReport",
					"style": {
						"navigationBarTitleText": "AI智能报告",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				}
			]
		},
		//拼音法抗遗忘
		{
			"root": "PYFpages",
			"name": "PYFpages",
			"pages": [{
					"path": "forgetReview",
					"style": {
						"navigationBarTitleText": "拼音法抗遗忘",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "dayLessonPreview",
					"style": {
						"navigationBarTitleText": "当日课程预览",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pastReview",
					"style": {
						"onReachBottomDistance": 100,
						// "enablePullDownRefresh": true,
						"navigationBarTitleText": "往期复习",
						"navigationStyle": "custom",
						"navigationBarBackgroundColor": "#FFFFFF",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "todayReview",
					"style": {
						"onReachBottomDistance": 100,
						"navigationBarTitleText": "今日复习",
						"navigationStyle": "custom",
						"navigationBarBackgroundColor": "#FFFFFF",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "lessonPreview",
					"style": {
						"navigationBarTitleText": "复习课程",
						"enablePullDownRefresh": false,
						"navigationStyle": "default"
					}
				},
				{
					"path": "preschoolVideo",
					"style": {
						"navigationBarTitleText": "学前视频",
						"enablePullDownRefresh": false,
						"navigationStyle": "default"
					}
				},
				{
					"path": "yfyReviewCheck",
					"style": {
						"navigationBarTitleText": "元辅音复习检测",
						"enablePullDownRefresh": false,
						"navigationStyle": "default"
					}
				},
				{
					"path": "dcReviewCheck",
					"style": {
						"navigationBarTitleText": "单词复习检测",
						"enablePullDownRefresh": false,
						"navigationStyle": "default"
					}
				},
				{
					"path": "reviewReport",
					"style": {
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				}
			]
		},
		{
			"root": "authen",
			"name": "authen",
			"pages": [{
					"path": "authen",
					"style": {
						"navigationBarTitleText": "实名认证",
						"enablePullDownRefresh": false,
						"navigationStyle": "default"
					}
				},
				{
					"path": "Privacyagree",
					"style": {
						"navigationBarTitleText": "隐私协议",
						"navigationStyle": "default"
					}
				},
				{
					"path": "useragree",
					"style": {
						"navigationBarTitleText": "用户服务协议",
						"navigationStyle": "default"
					}
				},
				{
					"path": "ProtocolList/ProtocolList",
					"style": {
						"navigationBarTitleText": "隐私政策",
						"enablePullDownRefresh": false,
						"navigationStyle": "default"
					}
				},
				{
					"path": "PrivacyagreeApp",
					"style": {
						"navigationBarTitleText": "隐私协议",
						"navigationStyle": "default"
					}
				}, {
					"path": "webview",
					"style": {
						"navigationBarTitleText": ""
					}
				}
			]
		},
		{
			"root": "news",
			"name": "news",
			"pages": [{
					"path": "news",
					"style": {
						"navigationBarTitleText": "消息",
						"enablePullDownRefresh": false,
						"navigationStyle": "default"
					}
				},
				{
					"path": "availableTime",
					"style": {
						"navigationBarTitleText": "当前可用时间",
						"enablePullDownRefresh": false,
						"navigationStyle": "default"
					}
				}
			]
		},
		{
			"root": "qyWechat",
			"name": "qyWechat",
			"pages": [{
					"path": "login_qywx",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "login_psd",
					"style": {
						"navigationBarTitleText": "登录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "bindQyWechat",
					"style": {
						"navigationBarTitleText": "绑定手机号",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "studentInfo",
					"style": {
						"navigationBarTitleText": "学员信息",
						"enablePullDownRefresh": false,
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#F3F8FC"
					}
				},
				{
					"path": "mettingDetail",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "CommentStar",
					"style": {
						"navigationBarTitleText": "学习反馈",
						"enablePullDownRefresh": false,
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#f3f8fc"
					}
				},
				{
					"path": "homeworkDetail",
					"style": {
						"navigationBarTitleText": "一课一练报告",
						"navigationStyle": "default"
					}
				},
				{
					"path": "takingOrder",
					"style": {
						"navigationBarTitleText": "未接单列表",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom",
						"navigationBarBackgroundColor": "#f3f8fc"
					}
				},
				{
					"path": "notice",
					"style": {
						// "navigationBarTitleText": "未接单列表",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom",
						"navigationBarBackgroundColor": "#f3f8fc"
					}
				},
				{
					"path": "formalClass",
					"style": {
						"enablePullDownRefresh": false,
						"navigationStyle": "custom",
						"navigationBarTitleText": "正式课"
					}
				},
				{
					"path": "testClass",
					"style": {
						"enablePullDownRefresh": false,
						"navigationStyle": "custom",
						"navigationBarTitleText": "试课单 "
					}
				},
				{
					"path": "formalClassMany",
					"style": {
						"enablePullDownRefresh": false,
						"navigationStyle": "custom",
						"navigationBarTitleText": "正式课接单"
					}
				},
				{
					"path": "testClassMany",
					"style": {
						"enablePullDownRefresh": false,
						"navigationStyle": "custom",
						"navigationBarTitleText": "试课单接单"
					}
				},
				{
					"path": "noticeDetail",
					"style": {
						"enablePullDownRefresh": false,
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#f3f8fc",
						"navigationBarTitleText": "体验课公告模板"
					}
				},
				{
					"path": "msgCount",
					"style": {
						"enablePullDownRefresh": false,
						"navigationStyle": "custom",
						"navigationBarBackgroundColor": "#f3f8fc",
						"navigationBarTitleText": "交付中心总结通知"
					}
				},
				{
					"path": "msgCountDetail",
					"style": {
						"enablePullDownRefresh": false,
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#f3f8fc",
						"navigationBarTitleText": "教练反馈总结"
					}
				},
				{
					"path": "bindGroup",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "feedBack",
					"style": {
						"navigationBarTitleText": "",
						"backgroundColor": "#F3F8FC"
					}
				},
				{
					"path": "judgeCode",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "coachManage",
					"style": {
						"enablePullDownRefresh": false,
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#f3f8fc",
						"navigationBarTitleText": "教练管理"
					}
				},
				{
					"path": "coachList",
					"style": {
						"enablePullDownRefresh": false,
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#f3f8fc",
						"navigationBarTitleText": "教练管理"
					}
				},

				{
					"path": "qrCode",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "setGroup",
					"style": {
						"navigationBarTitleText": "建群",
						"navigationStyle": "default"
					}
				},
				{
					"path": "downloadLink",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "changeGroup",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "groupOrder",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "onlyFeedBack",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "pinyin_study_feedback",
					"style": {
						"navigationBarTitleText": "学习反馈",
						"navigationStyle": "default",
						"backgroundColor": "#F3F8FC"
					}
				},
				{
					"path": "listen_study_feedback",
					"style": {
						"navigationBarTitleText": "学习反馈",
						"navigationStyle": "default",
						"backgroundColor": "#F3F8FC"
					}
				},
				{
					"path": "oneToMany_study_feedback",
					"style": {
						"navigationBarTitleText": "学习反馈",
						"navigationStyle": "default",
						"backgroundColor": "#F3F8FC"
					}
				},
				// 数学反馈
				{
					"path": "math_study_feedback",
					"style": {
						"navigationBarTitleText": "学习反馈",
						"navigationStyle": "default",
						"backgroundColor": "#F3F8FC"
					}
				}
			]
		},
		{
			"root": "share",
			"name": "share",
			"pages": [{
					"path": "pinyin_share_feedback",
					"style": {
						"navigationBarTitleText": "分享",
						"navigationStyle": "default"
					}
				},
				{
					"path": "share_feedback",
					"style": {
						"navigationBarTitleText": "分享",
						"navigationStyle": "default"
					}
				},
				{
					"path": "dxn_study_feedback",
					"style": {
						"backgroundColor": "#F3F8FC"
					}
				},
				{
					"path": "dxn_share_feedback",
					"style": {
						"navigationBarTitleText": "分享",
						"navigationStyle": "default"
					}
				},
				{
					"path": "xkt_study_feedback",
					"style": {
						"backgroundColor": "#F3F8FC"
					}
				},
				{
					"path": "xkt_share_feedback",
					"style": {
						"backgroundColor": "#F3F8FC",
						"navigationBarTitleText": "分享",
						"navigationStyle": "default"
					}
				},
				// 一对多学习反馈分享
				{
					"path": "oneToMany_share_feedback",
					"style": {
						"backgroundColor": "#F3F8FC",
						"navigationBarTitleText": "分享",
						"navigationStyle": "default"
					}
				},
				// 数学分享
				{
					"path": "math_share_feedback",
					"style": {
						"backgroundColor": "#F3F8FC",
						"navigationBarTitleText": "分享",
						"navigationStyle": "default"
					}
				}
			]
		},
		{
			"root": "ReadForget",
			"name": "ReadForget",
			"pages": [{
					"path": "index",
					"style": {
						"navigationBarTitleText": "超级阅读抗遗忘",
						"enablePullDownRefresh": false,
						"navigationStyle": "default"
						// "navigationBarBackgroundColor": "#F3F8FC"
					}
				},
				{
					"path": "todayForget",
					"style": {
						"navigationBarTitleText": "今日复习",
						"enablePullDownRefresh": false,
						"navigationStyle": "default"
						// "navigationBarBackgroundColor": "#F3F8FC"
					}
				},
				{
					"path": "pastForget",
					"style": {
						"navigationBarTitleText": "往期复习",
						"enablePullDownRefresh": false,
						"navigationStyle": "default"
						// "navigationBarBackgroundColor": "#F3F8FC"
					}
				},
				{
					"path": "forgetReview",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
						// "navigationBarBackgroundColor": "#F3F8FC"
					}
				},
				{
					"path": "ReviewCheckPoint",
					"style": {
						"navigationBarTitleText": "复习关卡",
						"enablePullDownRefresh": false,
						"navigationStyle": "default"
					}
				}, {
					"path": "ReviewBook",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "readReport",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				}


			]
		}
	],
	"uniIdRouter": {},
	"condition": {
		//模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
			"name": "", //模式名称
			"path": "", //启动页面，必选
			"query": "" //启动参数，在页面的onLoad函数里面得到
		}]
	}
}