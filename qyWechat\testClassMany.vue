<template>
  <page-meta :page-style="'overflow:' + (showTime ? 'hidden' : 'visible')"></page-meta>
  <view class="plr-20 pb-30">
    <u-navbar title="试课单接单" bgColor="#f3f8fc" placeholder>
      <view class="" slot="left">
        <u-icon name="arrow-left" size="24" bold color="#000" @click="goBack"></u-icon>
      </view>
      <view class="u-nav-slot" slot="center" style="font-weight: 600">{{ navBarTitle }}</view>
    </u-navbar>
    <view class="bg-ff plr-20 pb-40 radius-15 positionRelative">
      <block v-if="orderStatus.type == 1">
        <view class="flex-coum" :style="{ height: useHeight + 'rpx' }">
          <image :src="img1" style="width: 130rpx; height: 130rpx" mode="aspectFill"></image>
          <block v-if="!orderStatus.recvingTime && !orderStatus.teacherName && !orderStatus.teamName">
            <view class="mt-40" style="color: #666666; font-size: 30rpx">已被接单</view>
          </block>
          <block v-else>
            <view class="mt-40" style="color: #666666; font-size: 30rpx" v-if="orderStatus.teacherName">
              <text>已被</text>
              <text style="color: #000; font-weight: 700">{{ orderStatus.teacherName }}</text>
              <text>教练接单</text>
            </view>
            <view class="mt-40" style="color: #666666; font-size: 30rpx" v-if="orderStatus.teamName">
              <text>已被</text>
              <text style="color: #000; font-weight: 700">{{ orderStatus.teamName }}</text>
              <text>小组接单</text>
            </view>
            <view class="mt-40" style="color: #666666; font-size: 30rpx">接单时间：{{ orderStatus.recvingTime }}</view>
          </block>
        </view>
      </block>
      <block v-if="orderStatus.type == 2">
        <view class="flex-coum" :style="{ height: useHeight + 'rpx' }">
          <image :src="img2" style="width: 130rpx; height: 130rpx" mode="aspectFill"></image>
          <view class="mt-40" style="color: #666666; font-size: 30rpx; text-align: center">因未及时接单</view>
          <view class="mt-10" style="color: #666666; font-size: 30rpx; text-align: center">已被转移至其他交付中心</view>
        </view>
      </block>
      <block v-if="orderStatus.type == 3">
        <view>
          <view>
            <u-gap height="10"></u-gap>
            <!-- <u-divider></u-divider> -->
            <view style="margin: 0 20rpx; font-size: 30rpx; color: #333333">
              <u-row customStyle="margin-bottom: 40rpx;">
                <u-col :span="3" customStyle="font-weight: bold;">班级名称:</u-col>
                <u-col :span="8">{{ classList.className }}</u-col>
                <u-col :span="1"><u-tag text="班" size="mini" bgColor="#3f927a" customStyle="width:30rpx"></u-tag></u-col>
              </u-row>
              <u-row customStyle="margin-bottom: 40rpx">
                <u-col :span="3" customStyle="font-weight: bold;">分类:</u-col>
                <u-col :span="9">试课</u-col>
              </u-row>
              <u-row customStyle="margin-bottom: 40rpx">
                <u-col :span="3" customStyle="font-weight: bold;">课程类型:</u-col>
                <u-col :span="9">{{ classList.curriculumName }}</u-col>
              </u-row>
              <u-row>
                <u-col :span="3" customStyle="font-weight: bold;">学生姓名:</u-col>
                <u-col :span="9" v-if="classList.studentNames && classList.studentNames.length <= 15">{{ classList.studentNames }}</u-col>
              </u-row>
              <u-row customStyle="margin-top: 20rpx; color: #666" v-if="classList.studentNames && classList.studentNames.length > 15">
                <u-col>{{ classList.studentNames }}</u-col>
              </u-row>
              <u-row customStyle="margin: 40rpx 0">
                <u-col :span="3" customStyle="font-weight: bold;">时间:</u-col>
                <u-col :span="9" customStyle="white-space: break-spaces" v-if="classList.classTimeStr">
                  {{ classList.classTimeStr.slice(0, classList.classTimeStr.length - 11) }}
                  <br />
                  {{ classList.classTimeStr.slice(classList.classTimeStr.length - 11) }}
                </u-col>
              </u-row>
              <u-row customStyle="margin-bottom: 20rpx">
                <u-col :span="4" customStyle="font-weight: bold;">可接单时间:</u-col>
                <u-col :span="8">{{ classList.limitTime ? classList.limitTime : '无' }}</u-col>
              </u-row>
            </view>
            <u-divider style="margin-bottom: 0"></u-divider>
            <!-- tab栏 -->
            <!-- transform: 'scale(1.05)' -->
            <!-- [classListIndex].studentName.length * 12 -->
            <u-tabs
              :list="classList.studentList"
              keyName="studentName"
              @click="changeClassListIndex"
              :current="classListIndex"
              :activeStyle="{
                color: '#444',
                fontWeight: 'bold',
                fontSize: '30rpx'
              }"
              :inactiveStyle="{ color: '#444', fontSize: '30rpx' }"
              lineColor="#3f927a"
              :lineWidth="30"
            ></u-tabs>
          </view>
          <view class="information ptb-15 borderB">
            <view class="f-30 bold student-title">课程类型：</view>
            <view class="phone-input">
              <input type="text" :value="isClass ? classList.curriculumName : infolist.curriculumName" name="trialname" class="input" :disabled="true" />
            </view>
          </view>
          <view class="information ptb-15 borderB">
            <view class="f-30 bold student-title">试课人姓名：</view>
            <view class="phone-input">
              <input
                type="text"
                @input="inputName"
                :value="isClass ? infolist.realName : infolist.expName"
                name="trialname"
                placeholder="请输入试课人姓名"
                class="input"
                :disabled="isDisable"
              />
            </view>
          </view>
          <!-- 扩展字段 -->
          <template v-if="infolist.extendList && infolist.extendList.length">
            <view class="information ptb-15 borderB" :key="index" v-for="(item, index) in infolist.extendList">
              <view class="f-30 bold student-title">{{ item.fieldName }}:</view>
              <view class="phone-input">
                {{ item.fieldValue }}
              </view>
            </view>
          </template>
          <view class="information ptb-15 borderB">
            <view class="f-30 bold student-title">试课人年级：</view>
            <view class="phone-input ptb-5 positionRelative">
              <view class="uni-input">
                {{ getGradeVal(infolist.grade) }}
              </view>
            </view>
          </view>
          <view class="information ptb-15 borderB">
            <view class="f-30 bold student-title">试课人性别：</view>
            <view class="phone-input ptb-5 positionRelative">
              <view class="uni-input">
                {{ infolist.gender == 1 ? '男' : '女' }}
              </view>
            </view>
          </view>
          <view class="information ptb-30 borderB">
            <view class="f-30 bold student-title">试课人联系方式：</view>
            <view class="phone-input" style="padding-left: 0">
              <input :value="infolist.phone" name="number" placeholder="无" class="input" maxlength="11" :disabled="isDisable" />
            </view>
          </view>
          <view class="information ptb-30 borderB">
            <view class="f-30 bold student-title">
              <view class="bold">上课时间:</view>
            </view>
            <view class="uni-list-cell-db flex-s pr-20 flex-s positionRelative">
              <view>
                <view class="uni-input" style="white-space: break-spaces" v-if="classList.classTimeStr">
                  {{ classList.classTimeStr.slice(0, classList.classTimeStr.length - 11) }}
                  <br />
                  {{ classList.classTimeStr.slice(classList.classTimeStr.length - 11) }}
                </view>
              </view>
            </view>
          </view>

          <view class="information ptb-15 borderB">
            <view class="f-30 bold student-title">试课人所在区域：</view>
            <view class="regions-input flex-s ptb-20 pr-20 positionRelative">
              {{ place }}
            </view>
          </view>

          <view class="information flex-a-c ptb-15 borderB ptb-30 f-30">
            <view class="f-30 bold student-title">咨询师：</view>
            <view class="student-content">
              {{ infolist.counselor == '0' ? '自己' : infolist.counselor == '1' ? '上级推荐人' : infolist.counselor == '' ? '无' : infolist.counselor }}
            </view>
          </view>

          <view class="information pt-30 f-30">
            <view class="f-30 bold student-title">体验需求：</view>
          </view>
          <view class="m-30 remake-bg-view" style="padding-bottom: 60rpx">
            {{ infolist.remark ? infolist.remark : '' }}
          </view>

          <view class="tips" :style="{ height: svHeight + 'px' }" v-if="orderStatus.type == 3 && orderType == 0">
            <button v-if="classList.isShowReceiveButton" class="phone-btn" @click="receive">接单</button>
            <button class="phone-btn phone-btn1" @click="nextOrder">下一单</button>
          </view>
        </view>
      </block>
    </view>
  </view>
</template>

<script>
import dayjs from 'dayjs';
import longDate from '@/components/long-date/long-date.vue';
import { Debounce } from '@/utils/debounce.js';
export default {
  components: {
    longDate
  },
  data() {
    const currentDate = this.getDate({
      format: true
    });
    return {
      qrCode: '',
      codeShow: false,
      title: 'input',
      focus: false,
      inputValue: '',
      changeValue: '',
      mobile: '', // 推荐人手机号
      trialname: '', // 试课人姓名
      number: '', // 试课人手机号
      date: '', // 日期
      fristDate: '', // 月日周几
      lastDate: '', //上午/下午/晚上 + 具体时间
      show: false, // 是否显示日期
      region: '',
      gender: '', //性别
      range: [
        {
          value: 1,
          text: '男'
        },
        {
          value: 2,
          text: '女'
        }
      ],
      grade: '', //年级
      classList: {}, //班级信息
      classListIndex: 0, //班级信息索引
      isClass: false, //是否是班级类型/1v多
      infolist: {}, // 试课单回显信息
      classInfolist: [], // 缓存的班级学员信息列表

      place: '', // 区域

      gradelist: [],
      orderId: '',
      deliverMerchant: '',
      payStatus: '2', //1填写试课单   2查看试课单
      list: '',
      isShow: false, //是否显示按钮
      isDisable: true, //是否禁用
      useHeight: 0, //除头部之外高度

      svHeight: 50,

      imgHost: getApp().globalData.imgsomeHost,

      datevalue: '',
      startTime: '', // 开始时间
      showTime: false,
      time: '',
      index: 0, //
      current: 0, // 是否住校（是否需要咨询）

      radio: 0,
      val: 0,
      score: '', // 英语成绩
      residentialSchool: '', // 是否住校（是否需要咨询）
      classInstruction: '', // 英语课外辅导
      remark: '', // 体验需求
      flag: false, // 防止重复点击

      disabled: false,

      clientName: '', //客户姓名

      //咨询师选择
      counselor: '',
      counselorOther: 0,
      counselorSelf: 0,

      trialTimeData: [],
      normalDateBack: {},
      choseDateBack: {},
      sureChoseData: null,
      newDate: '', //新日期
      weekdaysShort: '周日_周一_周二_周三_周四_周五_周六'.split('_'),
      gradeNameArr: '一年级_二年级_三年级_四年级_五年级_六年级_初一_初二_初三_高一_高二_高三_大一_大二_大三_大四_其他_幼儿园'.split('_'),
      timeTipStr: '',
      orderType: 0,
      orderStatus: {},
      img1: 'https://document.dxznjy.com/automation/1721727506000',
      img2: 'https://document.dxznjy.com/automation/1721727522000',
      navBarTitle: '试课单接单',
      app: 0,
      isProcessingBack: false,
      needBackList: 1 // 1需要返回接单列表 0需要返回接单大厅退出小程序
    };
  },
  onReady() {
    let that = this;
    uni.getSystemInfo({
      //调用uni-app接口获取屏幕高度
      success(res) {
        // 可使用窗口高度，将px转换rpx
        let h = res.windowHeight * (750 / res.windowWidth);
        that.useHeight = h - 65;
      }
    });
  },
  onLoad(e) {
    console.log(e);
    // #ifdef APP-PLUS
    this.app = e.app;
    this.needBackList = e.needBackList ? e.needBackList : this.needBackList;
    // #endif
    this.orderId = e.orderId;
    this.isClass = e.isClass == 1;
    let token = e.token?.includes(',') ? e.token.split(',')[0] : e.token;
    let tel = e.tel?.includes(',') ? e.tel.split(',')[0] : e.tel;
    if (this.isClass && token && tel) {
      uni.setStorageSync('token', e.token?.includes(',') ? e.token.split(',')[0] : e.token);
      uni.setStorageSync('tel', e.tel?.includes(',') ? e.tel.split(',')[0] : e.tel);
    } // this.payStatus = e.payStatus;
    if (e.orderType) {
      this.orderType = e.orderType;
    }
    if (e.deliverMerchant) {
      this.deliverMerchant = e.deliverMerchant;
    }
    if (this.deliverMerchant) {
      this.getStatus();
    } else {
      this.orderStatus.type = 3;
      this.init();
    }
  },

  onShow() {
    if (this.deliverMerchant) {
      this.getStatus();
    } else {
      this.init();
    }
    if (this.payStatus == 1) {
      ///初始化时间选择
      this.normalDateBack = {
        timeWeek: this.getTimeWeek(),
        time: this.getTime2(),
        isRequest: true,
        valueArr: [0, 0]
      };
      this.seletTime(this.normalDateBack, true);
    }
  },
  onBackPress() {
    if (this.isProcessingBack) {
      return true; // 阻止默认返回行为
    }
    this.isProcessingBack = true;
    // 获取系统信息判断平台
    const systemInfo = uni.getSystemInfoSync();
    console.log('当前平台:', systemInfo.platform);

    if (systemInfo.platform === 'android') {
      if (this.needBackList == 1) {
        // 需要返回接单列表
        uni.redirectTo({
          url: '/qyWechat/takingOrder'
        });
      } else {
        // 需要返回接单大厅
        plus.runtime.quit(); // 调用退出应用
      }
    } else if (systemInfo.platform === 'ios') {
      plus.runtime.quit(); // 调用退出应用
    }
    return true; // 必须返回 true 才能阻止默认返回
  },
  onUnload() {
    if (!this.isProcessingBack) {
      // 正常卸载逻辑
      const systemInfo = uni.getSystemInfoSync();
      if (systemInfo.platform === 'android') {
        return true;
        // uni.redirectTo({
        //   url: '/qyWechat/takingOrder'
        // });
      } else if (systemInfo.platform === 'ios') {
        plus.runtime.quit(); // 调用退出应用
      }
    }
  },
  methods: {
    // 手机号脱敏('13912345678' 转换成 '139****5678') 第3位开始替换4个
    telHide(value) {
      if (!value) {
        return '';
      } else {
        let data = value.replace(/(\d{3})\d{4}(\d*)/, '$1****$2');
        return data;
      }
    },
    goBack() {
      // // #ifdef APP-PLUS
      // plus.runtime.quit();
      // // #endif
      // if (this.orderType == 2) {
      //   uni.navigateBack();
      // } else {
      //   uni.redirectTo({
      //     url: '/qyWechat/takingOrder'
      //   });
      // }
      if (this.needBackList == 1) {
        // 需要返回接单列表
        uni.redirectTo({
          url: '/qyWechat/takingOrder'
        });
      } else {
        // 需要返回接单大厅
        plus.runtime.quit(); // 调用退出应用
      }
    },
    getStatus() {
      let that = this;
      that.$http
        .get('/deliver/web/experience/orderMessageInfo', {
          id: that.orderId,
          type: 1,
          deliverMerchant: that.deliverMerchant
        })
        .then(({ data }) => {
          that.orderStatus = data.data;
          if (that.orderStatus.type != 3) {
            that.navBarTitle = ' ';
          } else {
            that.init();
          }
        });
    },
    receive: Debounce(function () {
      let that = this;
      let mobile = uni.getStorageSync('tel');
      // uni.showLoading({
      //   title: '正在接单'
      // });
      // 1v多班级接单（如果是班级类型
      that.$http
        .post(`/deliver/web/deliverClass/receiving?mobile=${mobile}&deliverClassId=${this.orderId}`)
        .then(({ data }) => {
          if (data.success) {
            // console.log(data.data);
            // let obj = data.data;
            // obj.type = 1;
            uni.showToast({
              title: '接单成功'
            });
            // #ifdef APP-PLUS
            setTimeout(() => {
              plus.runtime.quit();
            }, 1500);
            return;
            // #endif
            // uni.hideLoading();
            // let item = encodeURIComponent(JSON.stringify(obj));
            setTimeout(() => {
              uni.reLaunch({
                url: `/qyWechat/takingOrder`
              });
            }, 500);
          }
        })
        .catch((err) => {
          uni.hideLoading();
        });
    }),
    nextOrder: Debounce(function () {
      // 下一单
      this.$http
        .get('/deliver/web/deliverClass/nextDispatch', {
          id: this.orderId,
          type: 1
        })
        .then(({ data }) => {
          console.log(data);
          if (data.success) {
            // if (data.data.type == 1) {
            // uni.showLoading({
            //   title: '加载中'
            // });
            this.orderId = data.data;
            this.init();
            // } else {
            //   uni.navigateTo({
            //     url: `/qyWechat/formalClass?orderId=${data.data.id}`
            //   });
            // }
          } else {
            uni.showToast({
              title: data.message,
              icon: 'none'
            });
          }
        });
    }),
    // 创建群聊
    creteGroup(userId, externalUserId, chatName) {
      // console.log(userId, externalUserId, chatName);
      wx.qy.openEnterpriseChat({
        // 注意：userIds和externalUserIds至少选填一个，且userIds+externalUserIds总数不能超过2000，如果externalUserIds有微信联系人，则总数不能超过40人。
        //参与会话的企业成员列表，格式为userid1;userid2;...，用分号隔开。
        userIds: userId + ';',
        // 参与会话的外部联系人列表，格式为userId1;userId2;…，用分号隔开。
        // externalUserIds: 'wm6t28KgAAWM25G-r9nX1Dwe-Un_LNxg;wm6t28KgAAKLHeTRKwKUaDAyusyLvfnw;wm6t28KgAASJJmvPheXaRfVROch0G9eQ;',
        externalUserIds: externalUserId + ';',
        groupName: chatName, // 必填，会话名称。单聊时该参数传入空字符串""即可。
        chatId: '', //新建会话时，chatId必须为空串
        success: (res) => {
          // 回调
          var chatId = res.chatId; //返回chatId仅在企业微信3.0.36及以后版本支持；
          console.log(chatId, '1111111111111111111111111');
        },
        fail: (err) => {
          // 失败处理
          console.log(err, '222222222222222222222222');
        }
      });
    },
    ////NEW//试课时间/////Start////
    openTime() {
      if (!this.isDisable) {
        this.getTime();
        this.showTime = true;
        //打开时间定位
        if (this.sureChoseData) {
          console.log('定位已选时间坐标');
          if (this.sureChoseData.valueArr && this.sureChoseData.valueArr.length != 0) {
            console.log(this.sureChoseData.valueArr);
            this.choseDateBack = this.sureChoseData;
            this.choseDateBack.isRequest = true;
            this.seletTime(this.choseDateBack, false, true);

            this.$refs.choseDate.getDataforChoseIndex(this.sureChoseData.valueArr);
          }
        } else {
          console.log('定位当前时间坐标');
          this.choseDateBack = this.normalDateBack;
          this.choseDateBack.isRequest = true;
          this.seletTime(this.choseDateBack, false, true);

          let index = this.getCurHourIndex();
          this.$refs.choseDate.getDataforChoseIndex([0, index]);
        }
        this.$refs.dataShow.open();
      }
    },
    //修改查看试课单的时间
    getShowTime1(date) {
      if (date) {
        // let time = new Date(date);
        let time = new Date(date.replace(/-/g, '/')); //ios适配
        let dateTime = dayjs(time).format('MM月DD日');
        let week = dayjs(time).get('day');

        return `${dateTime} ${this.weekdaysShort[week]} `;
      }
      return '';
    },
    getShowTime2(date) {
      if (date) {
        let dayStage = '';
        let time = new Date(date.replace(/-/g, '/')); //ios适配
        let hour = dayjs(time).get('hour');
        let minute = dayjs(time).get('minute');
        let endHour = dayjs(time).add(1, 'hour').get('hour');
        if (hour >= 8 && hour < 12) {
          dayStage = '上午';
        } else if (hour >= 12 && hour < 17) {
          dayStage = '下午';
        } else if (hour >= 17 && hour < 24) {
          dayStage = '晚上';
        } else {
          dayStage = '';
        }
        return `${dayStage} ${this.addZero(hour)}:${this.addZero(minute)}~${this.addZero(endHour)}:${this.addZero(minute)}`;
      }
      return '';
    },
    addZero(data) {
      return data < 10 ? `0${data}` : data;
    },
    //当前时间 格式YYYY-MM-DD
    getTime2() {
      let nowDate = Date.now();
      let endTime = dayjs(nowDate).add(1, 'day').format('YYYY-MM-DD');
      return endTime;
    },
    //当前时间 格式 月 日 周
    getTimeWeek() {
      let nowDate = Date.now();
      let endTime = dayjs(nowDate).add(1, 'day').format('MM月DD日');
      let week = dayjs(nowDate).add(1, 'day').get('day');
      return endTime + ' ' + this.weekdaysShort[week];
    },
    cancelAtion() {
      this.showTime = false;
      this.$refs.dataShow.close();
    },
    confirm() {
      let dayStage = null;
      if (this.choseDateBack.hour.isNormal) {
        uni.showToast({
          icon: 'none',
          title: '时间段状态加载中~'
        });
        return;
      }
      // isSameOrAfter
      if (!dayjs(this.choseDateBack.time + ' ' + this.choseDateBack.hour.startTime).isAfter(dayjs(Date.now()).add(24, 'hour'))) {
        uni.showToast({
          icon: 'none',
          title: '请选择24小时后的时间~'
        });
        return;
      }
      if (!this.choseDateBack.hour.canReserve) {
        uni.showToast({
          icon: 'none',
          title: '该时段预约已满，请选择其他时段'
        });
        // this.timeTipStr = "该时段预约已满，请选择其他时段"
        // this.$refs.notifyPopup.open();
        // setTimeout(()=>{
        // 	this.$refs.notifyPopup.close();
        // },1500)
        return;
      }
      let time = this.timeToMinutes(this.choseDateBack.hour.startTime);

      if (time >= 480 && time < 720) {
        dayStage = '上午';
      } else if (time >= 720 && time < 1020) {
        dayStage = '下午';
      } else if (time >= 1020 && time < 1380) {
        dayStage = '晚上';
      } else {
        return dayStage;
      }

      // 确定后显示时间
      this.fristDate = this.choseDateBack.timeWeek;
      this.lastDate = `${dayStage} ${this.choseDateBack.hour.startTime}~${this.choseDateBack.hour.endTime}`;
      this.newDate = this.choseDateBack.time + ' ' + this.choseDateBack.hour.startTime;
      this.sureChoseData = this.choseDateBack;

      this.show = true;
      this.cancelAtion();
      this.$forceUpdate();
    },
    timeToMinutes(time) {
      const [hour, minute] = time.split(':');
      return parseInt(hour) * 60 + parseInt(minute);
    },

    seletTime(val, isnormal, openPopup) {
      console.log(val);
      //请求数据接口
      this.choseDateBack = val;

      if (val.isRequest) {
        //默认数据用于定位日期
        this.getNormalData(isnormal, openPopup);
        this.getServiceData(isnormal, openPopup);
      } else {
        this.choseDateBack.hour = this.trialTimeData[this.choseDateBack.valueArr[1]];
      }
    },
    //请求状态
    async getServiceData(isnormal, openPopup) {
      let res = await this.$http.get(`/deliver/app/common/selUseExperienceUsableTime?date=${this.choseDateBack.time}`);
      console.log(res);
      if (res && res.data && res.data.data) {
        this.trialTimeData = [];
        this.trialTimeData = res.data.data;
        this.timeDataOpen(isnormal, openPopup);
      }
    },
    //默认数据
    getNormalData(isnormal, openPopup) {
      console.log('--getNormalData--');
      this.trialTimeData = [];
      for (let i = 8; i < 23; i++) {
        let data1 = {};
        data1.startTime = i < 10 ? `0${i}:00` : `${i}:00`;
        data1.endTime = i + 1 < 10 ? `0${i + 1}:00` : `${i + 1}:00`;
        data1.canReserve = false;
        data1.isNormal = true;
        let data2 = {};
        data2.startTime = i < 10 ? `0${i}:30` : `${i}:30`;
        data2.endTime = i + 1 < 10 ? `0${i + 1}:30` : `${i + 1}:30`;
        data2.canReserve = false;
        data2.isNormal = true;
        this.trialTimeData.push(data1);
        this.trialTimeData.push(data2);
      }
      this.timeDataOpen(isnormal, openPopup);
    },
    timeDataOpen(isnormal, openPopup) {
      let index = this.getCurHourIndex();
      if (this.choseDateBack.time == this.getTime2()) {
        this.trialTimeData = this.trialTimeData.slice(index, this.trialTimeData.length);
      }
      this.$refs.choseDate.getDataforChoseDate(this.trialTimeData);
      if (isnormal) {
        let index = this.getCurHourIndex();
        this.choseDateBack.hour = this.trialTimeData[index];
        this.choseDateBack.valueArr[1] = index;
        this.normalDateBack.hour = this.trialTimeData[index];
        this.normalDateBack.valueArr[1] = index;
      } else {
        if (!openPopup) {
          this.choseDateBack.valueArr[1] = 0;
          this.choseDateBack.hour = this.trialTimeData[this.choseDateBack.valueArr[1]];
          this.$refs.choseDate.getDataforChoseIndex(this.choseDateBack.valueArr);
        }
      }
    },
    //定位到当前时间段
    getCurHourIndex() {
      let today = new Date();
      let currentHour = today.getHours();
      let currentMinute = today.getMinutes();

      let currentTime = [];
      if (currentMinute < 30) {
        //小于半小时 则hour 不变 min等于半小时
        currentTime = [currentHour, 30];
      } else {
        //大于等于半小时  则hour+1 min等于0
        currentTime = [currentHour + 1, 0];
      }
      for (let i = 0; i < this.trialTimeData.length; i++) {
        const [startHour, startMinute] = this.getStartTimeArr(this.trialTimeData[i].startTime);
        if (startHour == currentTime[0] && startMinute == currentTime[1]) {
          return i;
        }
      }
      return 0;
    },

    getStartTimeArr(time) {
      let timeArray = time.split(':');
      let hour = parseInt(timeArray[0]);
      let minute = parseInt(timeArray[1]);
      return [hour, minute];
    },
    ///NEW///试课时间/////End////

    formatter(type, value) {
      if (type === 'year') {
        return `${value}年`;
      }
      if (type === 'month') {
        return `${value}月`;
      }
      if (type === 'day') {
        return `${value}日`;
      }
      if (type === 'hour') {
        return `${value}时`;
      }
      if (type === 'minute') {
        return `${value}分`;
      }
      return value;
    },
    getTime() {
      let nowDate = Date.now();
      // let setDate = dayjs(date).unix() * 1000;
      let endTime = dayjs(nowDate).format('YYYY-MM-DD HH:mm');
      let endTimes = dayjs(nowDate).add(1, 'day');
      this.time = dayjs(endTimes).valueOf();
      console.log(nowDate, endTime);
    },
    changeCurrent(value) {
      this.current = 1;
      this.index = 0;
      this.residentialSchool = value;
    },
    changeIndex(e) {
      this.residentialSchool = e;
      this.current = 0;
      this.index = 1;
    },
    changeRadio(value) {
      this.radio = 1;
      this.val = 0;
      this.classInstruction = value;
    },
    changeVal(e) {
      this.radio = 0;
      this.val = 1;
      this.classInstruction = e;
    },
    //咨询师
    changeCounselorOther(value) {
      this.counselorOther = 1;
      this.counselorSelf = 0;
      this.counselor = value;
    },
    changeCounselorSelf(e) {
      this.counselorOther = 0;
      this.counselorSelf = 1;
      this.counselor = e;
    },

    cancel() {
      this.showTime = false;
    },

    handleSubmit(e) {
      console.log(e);
      // {year: "2023", month: "07", day: "11", hour: "15", minute: "21", seconds: '55'}
      // this.birthday = `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}:${seconds}`;
    },

    async init() {
      let that = this;
      await that.getGrade();
      if (that.payStatus == 2) {
        that.isShow = false;
        // that.$refs.trialLook.trialShow=true;
        that.getEchoinfo(true);
      } else {
        that.getEchoinfo(false);
      }
    },
    inputName(e) {
      this.trialname = e.detail.value;
    },
    inputScore(e) {
      this.score = e.detail.value;
    },
    inputClientName(e) {
      this.clientName = e.detail.value;
    },

    inputRemark(e) {
      this.remark = e.detail.value;
    },

    // 日期
    bindDateChange: function (e) {
      this.date = dayjs(e.value).format('YYYY-MM-DD HH:mm');
      console.log(this.date);
      this.show = true;
      this.showTime = false;
      this.$forceUpdate();
    },

    getDate(type) {
      const date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      if (type === 'start') {
        year = year - 60;
      } else if (type === 'end') {
        year = year + 2;
      }
      month = month > 9 ? month : '0' + month;
      day = day > 9 ? day : '0' + day;
      return `${year}-${month}-${day}`;
    },

    // 城市
    bindRegionChange: function (e) {
      this.region = e.detail.value;
    },
    getGradeVal(val) {
      if (val) {
        val -= 1;
        let index = 0;
        if (val <= 0) {
          index = 0;
        } else if (val >= this.gradeNameArr.length) {
          index = this.gradeNameArr.length - 1;
        } else {
          index = val;
        }
        return this.gradeNameArr[index];
      }
      return this.gradeNameArr[this.gradeNameArr.length - 1];
    },
    //性别下拉框
    choose(e) {
      this.gender = e;
    },
    //年级
    choice(e) {
      this.grade = e;
    },
    //年级
    async getGrade() {
      let res = await this.$http.get('/znyy/bvstatus/GradeType');
      if (res.data.success) {
        let list = res.data.data;
        if (this.gradelist.length == 0) {
          list.forEach((item) =>
            this.gradelist.push({
              value: Number(item.value),
              text: item.label,
              children: item.children,
              ext: item.ext
            })
          );
        }
      } else {
        this.$util.alter(res.data.message);
      }
    },
    // 新增试课单
    /*   async getTrial() {
          let _this = this;
          if (_this.flag) {
            return;
          }
          _this.flag = true;
          _this.disabled = true;
          if (_this.trialname == '') {
            _this.flag = false;
            _this.disabled = false;
            return $showError('请输入姓名');
          }
          if (_this.grade == '') {
            _this.flag = false;
            _this.disabled = false;
            return $showError('请选择年级');
          }
          if (_this.gender == '') {
            _this.flag = false;
            _this.disabled = false;
            return $showError('请选择性别');
          }
          if (!Util.isMobile(_this.number)) {
            _this.flag = false;
            _this.disabled = false;
            return $showError('请输入试课人正确的联系方式');
          }
          if (_this.newDate == null || _this.newDate == '') {
            // if (_this.date == null) {
            _this.flag = false;
            _this.disabled = false;
            return $showError('请选择期望试课时间');
          }
          if (_this.region == null) {
            return $showError('请选择试课人所在区域');
          }
          if (_this.score == '') {
            _this.flag = false;
            _this.disabled = false;
            return $showError('请输入英语成绩');
          }
          if (_this.residentialSchool === '') {
            _this.flag = false;
            _this.disabled = false;
            return $showError('请选择试课对象');
          }
          // 客戶姓名
          if (_this.residentialSchool === 1 && _this.clientName == '') {
            _this.flag = false;
            _this.disabled = false;
            return $showError('请输入客户姓名');
          }
          if (_this.counselor === '') {
            _this.flag = false;
            _this.disabled = false;
            return $showError('请选择咨询师');
          }
          if (_this.classInstruction === '') {
            _this.flag = false;
            _this.disabled = false;
            return $showError('请选择是否有英语课外辅导');
          }
          // 判断试课人是否添加过家长信息
          let res1 = await this.$http.get('/deliver/web/student/contact/info/getParentByMobile', {
            mobile: _this.number
            // referenceId: userId
          });
          if (!res1.data.data) {
            _this.flag = false;
            _this.disabled = false;
            _this.codeShow = true;
            return;
          }
  
          uni.showLoading();
          let data = {
            orderId: _this.orderId,
            expName: _this.trialname,
            grade: _this.grade,
            sex: _this.gender,
            expPhone: _this.number,
            expectTime: _this.newDate,
            // expectTime: _this.date,
            province: _this.region[0],
            city: _this.region[1],
            area: _this.region[2],
            score: _this.score,
            residentialSchool: '',
            classInstruction: _this.classInstruction,
            remark: _this.remark,
            clientName: _this.clientName,
            counselor: _this.counselor,
            experienceObject: _this.residentialSchool ? 1 : 2
          };
          let res = await this.$http.post('/zx/exp/save', data);
          // _this.flag= false;
          if (res) {
            uni.redirectTo({
              url: '/splitContent/officialAccount/staffCard?manageCode=123633&type=trialclass'
            });
            _this.flag = false;
            _this.disabled = false;
          } else {
            _this.flag = false;
            _this.disabled = false;
          }
          // uni.hideLoading();
        }, */

    // 试课单回显
    async getEchoinfo(isEdit) {
      let _this = this;
      uni.showLoading();
      // 如果是班级类型
      const { data } = await this.$http.get('/deliver/web/deliverClass/orderInfo', {
        deliverClassId: _this.orderId
      });
      uni.hideLoading();
      if (!data.data) {
        uni.showToast({
          icon: 'none',
          title: '该订单不存在'
        });
        return;
      }
      this.classListIndex = 0;
      let list = JSON.parse(JSON.stringify(data.data));
      // _this.classList.className = list.className; // 班级名称
      // _this.classList.curriculumName = list.curriculumName; // 课程类型
      // _this.classList.classTimeStr = list.classTimeStr; // 班级上课时间
      // _this.classList.limitTime = list.limitTime; // 可接单时间
      // _this.classList.studentNames = list.studentNames; // 学员姓名集合
      // _this.classList.isShowReceiveButton = list.isShowReceiveButton; // 是否显示接单按钮
      _this.classList = list;
      _this.classList.studentList = list.studentDtoList;
      _this.classInfolist = []; // 清空班级学员信息
      // 处理数据
      this.manageData();
    },
    // 班级选择学员tab
    changeClassListIndex(e) {
      this.classListIndex = e.index;
      this.manageData();
    },
    // 获取到数据后，对数据进行一些处理，
    async manageData() {
      let _this = this;
      // 先看是否有缓存数据，有的话直接返回缓存数据，没有的话再请求数据
      if (this.classInfolist[this.classListIndex]) {
        _this.infolist = this.classInfolist[this.classListIndex];
        return;
      }
      let id = _this.classList.studentList[this.classListIndex].expId; // 试课-expId 正课-contactInfoId
      const { data } = await this.$http.get('/deliver/web/experience/detail?id=' + id);
      _this.classInfolist[this.classListIndex] = data.data; // 保存数据
      _this.infolist = data.data;
      _this.infolist.expPhone = _this.telHide(_this.infolist.expPhone);
      _this.trialname = _this.infolist.expName;
      _this.number = _this.telHide(_this.infolist.expPhone);
      _this.place = _this.infolist.province + _this.infolist.city + _this.infolist.area;
    }
  }
};
</script>

<style lang="scss" scoped>
page {
  background-color: #fff;
}

.information {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 10rpx;
  padding-right: 10rpx;
  margin: 0 10rpx;
  color: #333333;
  font-size: 30rpx;

  /deep/.uni-icons {
    color: #fff !important;
  }
}

.phone-input {
  background: #fff;
  border-radius: 8rpx;
  width: 100%;
  height: 70rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
}

.name-input {
  background: #fff;
  border-radius: 8rpx;
  font-size: 30rpx;
  height: 70rpx;
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
  align-items: center;
}

.uni-list-cell-db {
  background: #fff;
  border-radius: 8rpx;
  width: 100%;
  font-size: 30rpx;
  display: flex;
  align-items: center;
}

/deep/.date_color {
  color: #000 !important;
}

/deep/.regions {
  color: #999 !important;
  font-size: 30upx;
}

.regions-input {
  width: 100%;
  font-size: 30rpx;
  display: flex;
  align-items: center;
}
.tips {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
/deep/.phone-btn {
  width: 310rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 45rpx;
  font-size: 30rpx;
  color: #fff !important;
  background: linear-gradient(to bottom, #88cfba, #1d755c);
}
/deep/.phone-btn1 {
  border: 1rpx solid #1d755c;
  color: #1d755c !important;
  background: #fff;
}

/deep/.uni-select {
  padding: 0 10rpx 0 0;
  border: 0;
}

/deep/.uni-select__input-placeholder {
  font-size: 30rpx;
}

/deep/.uni-select--disabled {
  background-color: #fff;
}

/deep/.uni-stat__select {
  height: 60rpx !important;
}

.borderB {
  border-bottom: 1px solid #efefef;
}

/deep/.uni-select__input-placeholder {
  color: #999 !important;
  font-size: 30rpx !important;
}

.icon_x {
  position: absolute;
  top: 28rpx;
  right: 0;
  z-index: 1;
}

.choose-icon2 {
  width: 35rpx;
  height: 35rpx;
}

.time-icon {
  /deep/.u-icon--right {
    position: absolute;
    right: 0;
    top: 20rpx;
  }
}

/deep/.u-picker__view {
  height: 600rpx !important;
}

.dialogBG {
  margin: 0 20rpx 20rpx 20rpx;
  height: 590rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

.top-button {
  margin-top: 20rpx;
  text-align: center;
  height: 80rpx;
  display: flex;
  justify-content: space-evenly;
}

.confirm-button {
  width: 210rpx;
  height: 80rpx;
  background-color: #2e896f;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  justify-content: center; /* 文本水平居中对齐 */
  align-items: center; /* 文本垂直居中对齐 */
}

.cancel-button {
  width: 210rpx;
  height: 80rpx;
  border: 1px solid #2e896f;
  color: #2e896f;
  font-size: 32rpx;
  display: flex;
  justify-content: center; /* 文本水平居中对齐 */
  align-items: center; /* 文本垂直居中对齐 */
  overflow: visible;
}

.borderT {
  border-top: 1px solid #efefef;
}
.review_close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 1;
}
.cartoom_image {
  width: 420rpx;
  position: absolute;
  top: -250rpx;
  left: 75rpx;
  z-index: -1;
}
.orderinfo {
  position: relative;
  width: 500rpx;
  font-size: 30rpx;
  border-radius: 24rpx;
  padding: 50rpx 0;
  background-color: #fff;
  .color {
    color: #7a7a7a;
  }
  .btns {
    display: flex;
    justify-content: space-around;
    margin-top: 20rpx;
  }
  .btn {
    width: 160rpx;
    height: 50rpx;
    border-radius: 50rpx;
    line-height: 50rpx;
    text-align: center;
    font-size: 20rpx;
  }
  .btn1 {
    color: #64a795;
    background-color: #ffffff;
    border: 1px solid #64a795;
  }
  .btn2 {
    background-color: #469880;
    // background-image: linear-gradient(60deg, #64b3f4 0%, #c2e59c 100%);
    color: #fff;
    border: 1px solid transparent;
  }
}
.flex-coum {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

::v-deep .u-cell__title-text.u-cell__title-text.u-cell__title-text {
  font-size: 32rpx;
  font-weight: bold;
}
.remake-bg-view {
  background: rgba(153, 153, 153, 0.1);
  border-radius: 12rpx;
  padding: 24rpx 35rpx;
  font-size: 30rpx;
  color: #000;
}
.student-title {
  width: 40%;
  flex-shrink: 0;
}
.student-content {
  width: 60%;
  flex-shrink: 0;
}
</style>
