<script>
  import Config from './utils/config';
  import { http, scrmHttp } from './utils/luch-request';
  export default {
    onLaunch: function (options) {
      console.log(options, '接收入参');

      if (options.path == 'pages/index/index') {
        uni.setStorageSync('token', options.referrerInfo.extraData.token);
        uni.setStorageSync('isAppRoleValue', options.referrerInfo.extraData.isAppRoleValue);
        uni.setStorageSync('tel', options.referrerInfo.extraData.tel);
        uni.setStorageSync('baseUrl', options.referrerInfo.extraData.baseUrl);
        uni.setStorageSync('appVersion', options.referrerInfo.extraData && options.referrerInfo.extraData.appVersion);
        Config.DXHost = options.referrerInfo.extraData.baseUrl;
        uni.setStorageSync('tempDxSource', options.referrerInfo.extraData.tempDxSource);
        //  DeliverTeamLeader
      } else {
        uni.setStorageSync('baseUrl', options.query && options.query.baseUrl);
        uni.setStorageSync('token', options.query && options.query.toden);
        uni.setStorageSync('tel', options.query && options.query.tel);
        uni.setStorageSync('appVersion', options.query && options.query.appVersion);
        Config.DXHost = options.query.baseUrl;
        uni.setStorageSync('tempDxSource', options.query.tempDxSource);
      }
      // uni.setStorageSync('token', '');
      var isQy = false;
      try {
        const res = wx.getSystemInfoSync();
        if (res && res.environment == 'wxwork') {
          isQy = true;
        } else {
          isQy = false;
        }
        uni.setStorageSync('isQy', isQy);
      } catch (e) {
        //TODO handle the exception
        uni.removeStorageSync('isQy');
      }
    },
    onShow: async function () {
      // 获取动态网关的标示，根据标示使用不同的BaseUrl
      // await this.getDynamicGatewayFlag();
      // // process.env.NODE_ENV === 'development' && (await this.getDynamicGatewayFlag());
      // #ifdef MP-WEIXIN
      this.checkForUpdate();
      // #endif
    },
    onHide: function () {
      // console.log('App Hide')
    },
    methods: {
      getDynamicGatewayFlag() {
        // this.globalData.isDynamicGatewayFlag = true;
        uni.$http
          .get('/zx/common/getDynamicGatewayFlag')
          .then((res) => {
            // console.log('🚀 ~ .getDynamicGatewayFlag ~ res:', res);
            let data = res.data.data;
            if (data) {
              Config.DXHost = 'https://.zhujiaoduan.com';
              http.setConfig((config) => {
                /* 设置全局配置 */
                config.baseUrl = Config.DXHost;
                return config;
              });
              scrmHttp.setConfig((config) => {
                /* 设置全局配置 */
                config.baseUrl = Config.DXHost;
                return config;
              });
            }
          })
          .catch((err) => {});
      },
      // 检测是否更新
      // #ifdef MP-WEIXIN
      checkForUpdate() {
        const _this = this;
        // 检查小程序是否有新版本发布
        const updateManager = uni.getUpdateManager();
        // 请求完新版本信息的回调
        updateManager.onCheckForUpdate((res) => {
          // console.log('onCheckForUpdate-res', res);
          //检测到新版本，需要更新，给出提示
          if (res && res.hasUpdate) {
            uni.showModal({
              title: '更新提示',
              content: '检测到新版本，是否下载新版本并重启？',
              success(res) {
                if (res.confirm) {
                  //用户确定下载更新小程序，小程序下载及更新静默进行
                  _this.downLoadAndUpdate(updateManager);
                } else {
                  // 若用户点击了取消按钮，二次弹窗，强制更新，如果用户选择取消后不需要进行任何操作，则以下内容可忽略
                  uni.showModal({
                    title: '温馨提示~',
                    content: '本次版本更新涉及到新的功能添加，旧版本无法正常访问的哦~',
                    confirmText: '确定更新',
                    cancelText: '取消更新',
                    success(res) {
                      if (res.confirm) {
                        //下载新版本，并重新应用
                        _this.downLoadAndUpdate(updateManager);
                      }
                    }
                  });
                }
              }
            });
          }
        });
      },
      // #endif
      // 下载小程序新版本并重启应用
      downLoadAndUpdate(updateManager) {
        const _this = this;
        uni.showLoading({
          title: '小程序更新中'
        });

        // //静默下载更新小程序新版本
        updateManager.onUpdateReady((res) => {
          console.log('onUpdateReady-res', res);
          uni.hideLoading();
          //新的版本已经下载好，调用 applyUpdate 应用新版本并重启
          updateManager.applyUpdate();
        });

        // 更新失败
        updateManager.onUpdateFailed((res) => {
          console.log('onUpdateFailed-res', res);
          // 新的版本下载失败
          uni.hideLoading();
          uni.showModal({
            title: '已经有新版本了哟~',
            content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~',
            showCancel: false
          });
        });
      }
    }
  };
</script>

<style>
  @import '@/static/alifont/icon.css';
  @import './common/common.css';
  /* @import 'utils/common.css'; */

  /*每个页面公共css */
  page {
    background: #f3f8fc;
  }

  .date {
    background-color: #fff;
  }

  /* 字体 */
  /* @font-face {
		font-family: 'ALIBBR';
		src: url("./static/font/AlibabaPuHuiTi-3-55-RegularL3.ttf");
	}

	@font-face {
		font-family: 'ALIBBM';
		src: url("./static/font/AlibabaPuHuiTi-2-65-Medium.otf");
	} */

  uni-scroll-view .uni-scroll-view::-webkit-scrollbar,
  .tab_content_inner::-webkit-scrollbar {
    display: none;
  }

  .uni-scroll-view-content {
    display: flex;
  }

  /*每个页面公共css */
  .classroomListBox uni-radio .uni-radio-input {
    width: 36rpx;
    height: 36rpx;
    margin: 0 18rpx 0 10rpx;
  }

  .connectTime_content .calendarHead {
    margin-bottom: 30rpx !important;
  }

  .userDatePicker .uni-date-editor .uni-date-editor--x {
    text-align: right !important;
  }

  .userDatePicker .uni-date-x,
  .userDatePicker .uni-date__x-input {
    padding: 0 !important;
  }

  .userDatePicker .uni-date-x .uni-icons {
    display: none !important;
  }

  .userDatePicker .uni-date-editor--x.uni-date-x--border {
    border: none !important;
  }

  .userDatePicker .uni-date-editor .uni-date__icon-clear {
    display: none !important;
  }

  .userDatePicker .uni-date-editor .uni-input-input {
    color: #000000;
  }

  .search_box .uni-searchbar .uni-searchbar__box {
    width: 650upx !important;
    height: 80upx !important;
    justify-content: flex-start;
  }

  .search_box .uni-searchbar__text-placeholder {
    color: #999999 !important;
  }

  .uni-picker-container .uni-picker-action.uni-picker-action-confirm {
    color: #0398ef !important;
  }

  .uni-mask {
    filter: blur(4upx);
    backdrop-filter: blur(20upx);
  }

  .uni-picker-container .uni-picker-custom {
    width: 710upx;
    border-radius: 12upx;
    margin: 20upx;
    overflow: hidden;
  }

  /* swiper */
  .userSwiper uni-swiper .uni-swiper-dot {
    width: 30upx;
    height: 10upx;
    background: #c1e1f5;
    border-radius: 6upx;
  }

  .userSwiper uni-swiper .uni-swiper-dots-horizontal {
    bottom: 0;
  }

  .userSwiper uni-swiper .uni-swiper-dot-active {
    background-color: #0398ef !important;
  }

  /* collapse */
  .needDealtPage .uni-collapse-item__title-box,
  .needDealtPage .uni-collapse-item__wrap {
    background-color: #f6f6f6 !important;
  }

  .needDealtPage .uni-collapse-item__wrap .item {
    background: none;
    margin-bottom: 40upx;
    height: auto !important;
  }

  .needDealtPage .uni-collapse-item__title {
    background-color: #f6f6f6;
  }

  .needDealtPage .option-box {
    width: 98upx !important;
    height: 172upx !important;
  }

  .needDealtPage .option-item {
    background: #ce1f1f !important;
    border-radius: 14upx !important;
  }

  .needDealtPage .item-container {
    border-radius: 14px !important;
  }

  /* progress */
  .feedback_progress .uni-progress-bar {
    border-radius: 4upx !important;
  }

  .feedback_progress .uni-progress-bar {
    border-radius: 4upx 0upx 0upx 4upx;
  }

  .scroll-list-wrap .scroll-view .scroll-content {
    margin: 0 auto !important;
  }

  .kindRewardPage .uni-scroll-view-content {
    display: block !important;
  }

  .audio-main uni-slider .uni-slider-handle-wrapper {
    height: 10upx !important;
  }

  .scroll-list-wrap .scroll-view .scroll-content {
    margin: 0 auto !important;
  }

  .kindRewardPage .uni-scroll-view-content {
    display: block !important;
  }

  .audio-main uni-slider .uni-slider-handle-wrapper {
    height: 10upx !important;
  }

  /* 去除所以button边框 */
  button::after {
    border: none !important;
  }
</style>
