<template>
  <view class="pagebg plr-30" :style="{ height: useHeight + 'rpx' }">
    <view class="titleView">
      <uni-icons type="left" size="20" color="#000" @click="gotoBack()"></uni-icons>
      <text class="page_title">提现</text>
    </view>

    <view class="mar_t60 c-66 f-30">可提现金额（元）</view>
    <view class="mar_t26">
      <text class="c-00 f-30">¥</text>
      <text class="c-00 f-38 ml-15">{{ withdrawable }}</text>
    </view>

    <view class="mar_l22 mar_t100 c-ff f-28">提现金额</view>
    <view class="mar_l22 mar_t26 c-ff flex-a-c">
      <text class="f-60 mr-10">¥</text>
      <input v-model="inputValue" type="digit" name="amount" class="f-50 bold flex-box money_height mt-10" />
    </view>

    <view class="mar_l22 mar_t110 c-00 f-28 font-b font-height">提现须知</view>
    <view class="mar_l22 font-height pr-30">
      <view class="f-28 c-66">
        1、
        <text class="c-fea font-b">可提现金额为到期可提现 ,但未提现的佣金与绩效</text>
      </view>
      <view class="f-28 c-66">2、为保证提现成功，请先完成实名认证绑卡操作 ，银行卡信息审核通过后方可进行提现操作。</view>
      <view class="f-28 c-66">3、提现时间段:8:00 - 20:00，到账时间:提现成功后一般将在1-2个工作日左右到账。</view>

      <view class="flex-c" style="width: 100%">
        <button class="Withdrawal f-30 c-ff t-c" :disabled="disabled" @click="submit_s">确定提现</button>
      </view>
    </view>

    <!-- 提现金额不能小于1元提示 -->
    <uni-popup ref="notifyPopup" type="top" mask-background-color="rgba(0,0,0,0)">
      <view class="t-c bg-ff flex-c ptb-25 radius-50 mt-80 notify">
        <u-icon name="error-circle-fill" color="#FA370E" size="24"></u-icon>
        <view class="f-34 ml-15">低于1元不可提现</view>
      </view>
    </uni-popup>

    <!-- 加载弹窗提示 -->
    <uni-popup ref="loadingPopup" type="center" :is-mask-click="false">
      <view class="bg-ff radius-15 t-c">
        <view class="loadingpadding">
          <image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
          <view class="mt-30">加载中...</view>
          <view>请耐心等候</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { http } = require('@/utils/luch-request/index.js');
  export default {
    data() {
      return {
        useHeight: 0,
        withdrawable: '', // 可提现
        userCode: '',
        inputValue: '',
        disabled: false,
        sourceOrderId: '',
        key: ''
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h;
        }
      });
    },
    onLoad(e) {
      this.withdrawable = Number(e.withdrawable);
      this.userCode = e.userCode;
      this.key = e.key;
    },
    onShow() {
      this.getRealName();

      // this.$refs.notifyPopup.open();
    },
    methods: {
      gotoBack() {
        uni.navigateBack();
      },

      async getRealName() {
        // 判断用户是否实名认证
        let that = this;
        let res = await http.get('/mps/user/info/user/code', {
          userCode: this.userCode
        });
        if (res.data.success) {
          if (res.data.data.signContractStatus != 1) {
            uni.showModal({
              title: '提示',
              content: '实名认证未完成，前往认证',
              showCancel: false,
              success: function (res) {
                if (res.confirm) {
                  uni.redirectTo({
                    url: '/authen/authen?userCode=' + that.userCode
                  });
                  // uni.redirectTo({
                  //   url: '/splitContent/authen/webview?url=' + encodeURIComponent(_this.signurl)
                  // });
                } else if (res.cancel) {
                  console.log('用户点击取消');
                }
              }
            });
          }
        }
      },

      // 提交提现申请
      async submit_s() {
        let _this = this;
        if (!_this.inputValue) {
          _this.$util.alter('提现金额不能为空');
          return false;
        }

        if (_this.inputValue > _this.withdrawable) {
          _this.$util.alter('可提现余额不足');
          return false;
        }

        if (_this.inputValue < 0.01) {
          _this.$refs.notifyPopup.open();
          setTimeout(() => {
            _this.$refs.notifyPopup.close();
          }, 1500);
          return false;
        }

        if (_this.disabled) {
          return false;
        }
        _this.disabled = true;
        _this.$refs.loadingPopup.open();
        let data = {
          userCode: this.userCode,
          amount: Number(_this.inputValue) * 100,
          remark: '',
          backStyle: 'DUBBO'
        };
        console.log(data, 'data1111111111');

        let data1 = {
          userCode: this.userCode,
          withdrawAmount: Number(_this.inputValue),
          remark: '',
          backStyle: 'DUBBO'
        };
        // let res = this.key == 1 ? await http.put('/mps/order/withdraw/unified', data) : await http.post('/flexpay/order/withdraw/unified', data1);
        let res = await http.put('/mps/order/withdraw/unified', data);
        _this.disabled = false;
        _this.$refs.loadingPopup.close();
        if (res.data.success) {
          _this.$util.alter('提现成功');
          _this.inputValue = '';
          uni.setStorageSync('withdrawal', true);
          uni.navigateBack();
        }
      }

      // 申请回调
      // async withdrawCallback(data) {
      // 	let _this = this;
      // 	let res = await http.put('/mps/order/withdraw/unified', data);
      // 	if (res.data.success) {
      // 		_this.$util.alter('提现成功');
      // 		_this.inputValue = '';
      // 		uni.navigateBack()
      // 	}
      // }
    }
  };
</script>

<style scoped lang="scss">
  .pagebg {
    width: 100%;
    box-sizing: border-box;
    background: url('https://document.dxznjy.com/dxSelect/fourthEdition/wallet-bgc1.png') 100% 100% no-repeat;
    background-size: 100% 100%;
    padding-left: 30rpx;
    position: relative;
  }

  .titleView {
    width: 100%;
    padding-top: 80rpx;
    position: relative;
  }

  .page_title {
    font-size: 34rpx;
    font-weight: bold;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  .mar_t26 {
    margin-top: 26rpx;
  }

  .mar_t60 {
    margin-top: 60rpx;
  }

  .mar_t100 {
    margin-top: 100rpx;
  }

  .mar_t110 {
    margin-top: 110rpx;
  }

  .mar_l22 {
    margin-left: 22rpx;
  }

  .f-30 {
    font-size: 30rpx;
  }

  .f-60 {
    font-size: 60rpx;
  }

  .f-38 {
    font-size: 38rpx;
  }

  .f-28 {
    font-size: 28rpx;
  }

  .c-00 {
    color: #000000;
  }

  .c-66 {
    color: #666666;
  }

  .c-ff {
    color: #fff;
  }

  .c-orange {
    color: #ea6031;
  }

  .font-b {
    font-weight: bold;
  }

  .font-height {
    line-height: 50rpx;
  }

  .Withdrawal {
    position: absolute;
    bottom: 60rpx;
    width: 586rpx;
    height: 90rpx;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    border-radius: 45rpx;
    line-height: 90rpx;
    margin: 30rpx auto 0 auto;
  }

  .notify {
    box-shadow: 0rpx 0rpx 20rpx #e0e0e0;
  }

  /deep/.uni-popup__wrapper {
    padding: 0 60rpx !important;
  }

  .loadingImg {
    width: 120rpx;
    height: 120rpx;
  }

  .loadingpadding {
    padding: 85rpx 250rpx;
  }
</style>
