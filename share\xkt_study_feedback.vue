<template>
  <view :class="isLoading ? 'xkt_disable_scroll' : ''">
    <page-meta :page-style="'overflow:' + (showReviewTimeDialog ? 'hidden' : 'visible')"></page-meta>
    <view class="nav-title">
      <view class="status_bar">
        <uni-icons @click="Back" type="left" color="#a8a8a8" size="24" class="icon_img"></uni-icons>
        {{ this.trialclass ? (triallist.curriculumName || '') + '体验结果反馈' : '学习反馈' }}
      </view>
    </view>

    <view class="container">
      <view class="card" v-if="trialclass">
        <view class="white-content" style="position: relative">
          <view @click="refresh(id)" class="refresh-view">
            <image class="width30" src="/static/images/shuaxin.png" mode=""></image>
            <view class="f-28" style="color: #2e896f; margin-left: 4rpx">刷新</view>
          </view>
          <view class="text-content">日期：{{ triallist.dateTime }}</view>
          <view class="text-content">姓名：{{ triallist.studentName }}</view>
          <view class="text-content">课程类型：{{ triallist.curriculumName }}</view>
          <view class="text-content">年级：{{ triallist.gradeName }}</view>
          <view class="text-content">学员编号：{{ triallist.studentCode }}</view>
          <view class="text-content">上课用时：{{ triallist.studyHour }}小时</view>
          <!-- 只有学考通类型的课程大类展示授课视频 -->
          <!-- 版本1授课视频单选注释 -->
          <!-- <view v-if="triallist.curriculumCode == 'XKT'">
            <view v-if="hasSGradeName" class="marginTop30">
              <picker mode="multiSelector" :range="multiData" :value="multiIndex" @columnchange="onColumnChange" @change="onCascadeChange">
                <view class="picker">
                  <text style="color: rgba(245, 114, 114); margin-right: 10rpx">*</text>
                  <text class="picker-text">授课视频：{{ selectedValues.join(' / ') || '请选择' }}</text>
                  <u-icon name="arrow-right" size="14" class="picker-icon"></u-icon>
                </view>
              </picker>
            </view>
            <view v-else class="picker marginTop30">
              <text style="color: rgba(245, 114, 114); margin-right: 10rpx">*</text>
              <text class="picker-text">
                授课视频：{{ triallist.xktStatisticsDto.xktGradeName }}/{{ triallist.xktStatisticsDto.xktCourseName }}/{{ triallist.xktStatisticsDto.xktVideoName }}
              </text>
            </view>
          </view> -->
        </view>
        <view v-if="XKTCurriculumCodeArr.includes(triallist.curriculumCode)" class="white-content marginTop30">
          <view style="height: 44rpx; line-height: 45rpx">
            <text style="color: #ff6359; margin: 0 16rpx 0 8rpx">*</text>
            <text style="font-weight: 500; font-size: 28rpx; color: #000000">授课视频</text>
          </view>
          <view style="height: 1rpx; background-color: #efefef; margin-top: 21rpx; margin-bottom: 11rpx"></view>
          <view>
            <picker mode="selector" @change="bindPickerGradeName" :value="gradeIndex" :range="gradeRange" :disabled="!Feedback && !isEdit">
              <view class="grade_picker">{{ selectedGrade.gradeName || '请选择学段' }}</view>
            </picker>
          </view>
          <view>
            <picker mode="selector" @change="bindPickerCourse" :value="courseIndex" :range="courseRange" :disabled="!Feedback && !isEdit">
              <view class="grade_picker">{{ selectedCourse.courseName || '请选择课程名称' }}</view>
            </picker>
          </view>
          <view class="video_list">
            <view :class="videoI.feedBackType ? 'video_item' : 'video_item_check'" v-for="(videoI, index) in videoListShow" :key="index" @click="changeFeedBackType(videoI)">
              <view style="width: 100%">{{ videoI.videoName }}</view>
            </view>
          </view>
          <view class="more_btn" v-if="isMore" @click="getMoreVideo">查看更多</view>
        </view>
        <view v-if="XKTCurriculumCodeArr.includes(triallist.curriculumCode)" class="xkt_table">
          <!-- 模块 1：课堂实时监测 -->
          <view class="table_title">课堂实时监测</view>

          <!-- 学习准备 -->
          <view style="padding: 27rpx 30rpx; font-weight: 400">
            <view class="study_choose" style="width: 128rpx"><view>学习准备</view></view>

            <!-- 1.预习任务完成 -->
            <view class="radio_title">1.预习任务完成</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="addFeedbackTableCo.preparationCompleted == itemLa.value ? 'active' : ''"
                @click="addFeedbackTableClick(itemLa, 'preparationCompleted')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.preparationCompleted == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>

            <!-- 2.进门测试题目正确率 -->
            <view class="radio_title">2.进门测试题目正确率</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.entryTestAccuracy == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'entryTestAccuracy')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.entryTestAccuracy == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>
            <view class="mark" style="margin-top: 29rpx">
              <u--textarea
                v-model="addFeedbackTableCo.entryTestRate"
                :value="addFeedbackTableCo.entryTestRate"
                :disabled="!Feedback && !isEdit"
                placeholder="备注（详细记录），请输入……"
                count
                placeholderStyle="color:#b0b0b6"
                height="146rpx"
                maxlength="30"
                border="none"
                class="custom-textarea"
              ></u--textarea>
            </view>
          </view>

          <!-- 注意力集中度 -->
          <view style="padding: 27rpx 30rpx; font-weight: 400">
            <view class="study_choose"><view>注意力集中度</view></view>

            <view class="radio_title">1.眼神跟随教师/屏幕</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.eyeFollowingScore == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'eyeFollowingScore')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.eyeFollowingScore == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>

            <view class="radio_title">2.无关操作<=2次/节课</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.irrelevantActions == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'irrelevantActions')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.irrelevantActions == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>
          </view>

          <!-- 课堂参与度 -->
          <view style="padding: 27rpx 30rpx; font-weight: 400">
            <view class="study_choose"><view>课堂参与度</view></view>

            <view class="radio_title">1.主动回答问题>=1次/节课</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="addFeedbackTableCo.activeAnswers == itemLa.value ? 'active' : ''"
                @click="addFeedbackTableClick(itemLa, 'activeAnswers')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.activeAnswers == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>

            <view class="radio_title">2.互动次数和质量</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.interactionCount == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'interactionCount')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.interactionCount == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>
          </view>

          <!-- 模块 2：教学效果观察 -->
          <view class="table_title">教学效果观察</view>

          <!-- 知识掌握 -->
          <view style="padding: 27rpx 30rpx; font-weight: 400">
            <view class="study_choose" style="width: 128rpx"><view>知识掌握</view></view>

            <view class="radio_title">1.课后留题正确率>=70%</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.postClassAccuracy == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'postClassAccuracy')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.postClassAccuracy == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>

            <view class="radio_title">2.课后错题讲解+知识点回顾质量</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.mistakeKnowledgeReviewQuality == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'mistakeKnowledgeReviewQuality')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.mistakeKnowledgeReviewQuality == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>
          </view>

          <!-- 精彩时刻 -->
          <view style="padding: 27rpx 30rpx; font-weight: 400">
            <view class="study_choose" style="width: 128rpx"><view>精彩时刻</view></view>

            <view class="radio_title">1.听课状态</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.learningStatus == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'learningStatus')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.learningStatus == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>
          </view>

          <!-- 情绪与态度 -->
          <view style="padding: 27rpx 30rpx; font-weight: 400">
            <view class="study_choose" style="width: 152rpx"><view>情绪与态度</view></view>

            <view class="radio_title">1.学习积极性</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.learningEngagement == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'learningEngagement')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.learningEngagement == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>

            <view class="radio_title">2.是否有厌学情绪，抵触心理</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.resistanceSigns == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'resistanceSigns')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.resistanceSigns == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>
          </view>
        </view>

        <view class="white-content marginTop30" style="height: 420rpx">
          <view class="flex-view">
            <view class="text-content">教练评语：</view>
            <view v-if="Feedback || isEdit" class="flex-view-text" @click="cleanFeedback()">清空</view>
          </view>
          <!-- 反馈 -->
          <view style="max-height: 300upx">
            <u--textarea
              v-if="Feedback || isEdit"
              :disabled="!Feedback && !isEdit"
              class="feedback"
              v-model="feedback"
              :value="feedback"
              placeholder="请输入"
              count
              placeholderStyle="color:#b0b0b6"
              height="165"
              maxlength="200"
            ></u--textarea>
            <view v-else class="font26 color_black33 feedbackBoxNew marginBottom10">
              {{ feedback }}
            </view>
          </view>
        </view>
        <view class="button-sp-area marginTop40 flexs">
          <button v-if="!Feedback && !isEdit" class="mini-btn refresh" type="default" size="mini" @click="editOption()">修改</button>
          <button v-if="Feedback || isEdit" class="mini-btn background_green_two" type="default" size="mini" @click="addOrLook(id)">确定</button>
          <button v-else class="mini-btn background_green_two" type="default" size="mini" @click="shareJump">分享</button>
        </view>
      </view>
      <!-- 正式学员反馈 -->
      <view class="card" v-else>
        <view class="white-content" style="position: relative">
          <view @click="refresh(id)" class="refresh-view">
            <image class="width30" src="/static/images/shuaxin.png" mode=""></image>
            <view class="f-28" style="color: #2e896f; margin-left: 4rpx">刷新</view>
          </view>
          <view class="text-content">日期：{{ backlist.dateTime }}</view>
          <view class="text-content">姓名：{{ backlist.studentName }}</view>
          <view class="text-content">课程类型：{{ backlist.curriculumName }}</view>
          <view class="text-content">年级：{{ backlist.gradeName }}</view>
          <view class="text-content">学员编号：{{ backlist.studentCode }}</view>
          <view class="text-content">上课用时：{{ backlist.studyHour }}小时</view>
          <view class="text-content">已购{{ backlist.curriculumName }}学时：{{ backlist.totalCourseHours }}小时</view>
          <view class="text-content">剩余{{ backlist.curriculumName }}学时：{{ backlist.leaveCourseHours }}小时</view>
          <!-- 只有学考通类型的课程大类展示授课视频 -->
          <!-- 版本1授课视频单选注释 -->
          <!-- <view v-if="backlist.curriculumCode == 'XKT'">
            <view v-if="hasGradeName" class="marginTop30">
              <picker mode="multiSelector" :range="multiData" :value="multiIndex" @columnchange="onColumnChange" @change="onCascadeChange">
                <view class="picker">
                  <text style="color: rgba(245, 114, 114); margin-right: 10rpx">*</text>
                  <text class="picker-text">授课视频：{{ selectedValues.join(' / ') || '请选择' }}</text>
                  <u-icon name="arrow-right" size="14" class="picker-icon"></u-icon>
                </view>
              </picker>
            </view>
            <view v-else class="picker marginTop30">
              <text style="color: rgba(245, 114, 114); margin-right: 10rpx">*</text>
              <text class="picker-text">
                授课视频：{{ backlist.xktStatisticsDto.xktGradeName }}/{{ backlist.xktStatisticsDto.xktCourseName }}/{{ backlist.xktStatisticsDto.xktVideoName }}
              </text>
            </view>
          </view> -->
        </view>
        <view v-if="XKTCurriculumCodeArr.includes(backlist.curriculumCode)" class="white-content marginTop30">
          <view style="height: 44rpx; line-height: 45rpx">
            <text style="color: #ff6359; margin: 0 16rpx 0 8rpx">*</text>
            <text style="font-weight: 500; font-size: 28rpx; color: #000000">授课视频</text>
          </view>
          <view style="height: 1rpx; background-color: #efefef; margin-top: 21rpx; margin-bottom: 11rpx"></view>
          <view>
            <picker mode="selector" @change="bindPickerGradeName" :value="gradeIndex" :range="gradeRange" :disabled="!isFeedback && !isEdit">
              <view class="grade_picker">{{ selectedGrade.gradeName || '请选择学段' }}</view>
            </picker>
          </view>
          <view>
            <picker mode="selector" @change="bindPickerCourse" :value="courseIndex" :range="courseRange" :disabled="!isFeedback && !isEdit">
              <view class="grade_picker">{{ selectedCourse.courseName || '请选择课程名称' }}</view>
            </picker>
          </view>
          <view class="video_list">
            <view :class="videoI.feedBackType ? 'video_item' : 'video_item_check'" v-for="(videoI, index) in videoListShow" :key="index" @click="changeFeedBackType(videoI)">
              <view style="width: 100%">{{ videoI.videoName }}</view>
            </view>
          </view>
          <view class="more_btn" v-if="isMore" @click="getMoreVideo">查看更多</view>
        </view>

        <view v-if="XKTCurriculumCodeArr.includes(backlist.curriculumCode)" class="xkt_table">
          <!-- 模块 1：课堂实时监测 -->
          <view class="table_title">课堂实时监测</view>

          <!-- 学习准备 -->
          <view style="padding: 27rpx 30rpx; font-weight: 400">
            <view class="study_choose" style="width: 128rpx"><view>学习准备</view></view>

            <!-- 1.预习任务完成 -->
            <view class="radio_title">1.预习任务完成</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="addFeedbackTableCo.preparationCompleted == itemLa.value ? 'active' : ''"
                @click="addFeedbackTableClick(itemLa, 'preparationCompleted')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.preparationCompleted == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>

            <!-- 2.进门测试题目正确率 -->
            <view class="radio_title">2.进门测试题目正确率</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.entryTestAccuracy == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'entryTestAccuracy')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.entryTestAccuracy == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>
            <view class="mark" style="margin-top: 29rpx">
              <u--textarea
                v-model="addFeedbackTableCo.entryTestRate"
                :value="addFeedbackTableCo.entryTestRate"
                :disabled="!isFeedback && !isEdit"
                placeholder="备注（详细记录），请输入……"
                count
                placeholderStyle="color:#b0b0b6"
                height="146rpx"
                maxlength="30"
                border="none"
                class="custom-textarea"
              ></u--textarea>
            </view>
          </view>

          <!-- 注意力集中度 -->
          <view style="padding: 27rpx 30rpx; font-weight: 400">
            <view class="study_choose"><view>注意力集中度</view></view>

            <view class="radio_title">1.眼神跟随教师/屏幕</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.eyeFollowingScore == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'eyeFollowingScore')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.eyeFollowingScore == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>

            <view class="radio_title">2.无关操作<=2次/节课</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.irrelevantActions == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'irrelevantActions')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.irrelevantActions == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>
          </view>

          <!-- 课堂参与度 -->
          <view style="padding: 27rpx 30rpx; font-weight: 400">
            <view class="study_choose"><view>课堂参与度</view></view>

            <view class="radio_title">1.主动回答问题>=1次/节课</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="addFeedbackTableCo.activeAnswers == itemLa.value ? 'active' : ''"
                @click="addFeedbackTableClick(itemLa, 'activeAnswers')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.activeAnswers == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>

            <view class="radio_title">2.互动次数和质量</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.interactionCount == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'interactionCount')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.interactionCount == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>
          </view>

          <!-- 模块 2：教学效果观察 -->
          <view class="table_title">教学效果观察</view>

          <!-- 知识掌握 -->
          <view style="padding: 27rpx 30rpx; font-weight: 400">
            <view class="study_choose" style="width: 128rpx"><view>知识掌握</view></view>

            <view class="radio_title">1.课后留题正确率>=70%</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.postClassAccuracy == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'postClassAccuracy')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.postClassAccuracy == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>

            <view class="radio_title">2.课后错题讲解+知识点回顾质量</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.mistakeKnowledgeReviewQuality == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'mistakeKnowledgeReviewQuality')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.mistakeKnowledgeReviewQuality == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>
          </view>

          <!-- 精彩时刻 -->
          <view style="padding: 27rpx 30rpx; font-weight: 400">
            <view class="study_choose" style="width: 128rpx"><view>精彩时刻</view></view>

            <view class="radio_title">1.听课状态</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.learningStatus == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'learningStatus')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.learningStatus == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>
          </view>

          <!-- 情绪与态度 -->
          <view style="padding: 27rpx 30rpx; font-weight: 400">
            <view class="study_choose" style="width: 152rpx"><view>情绪与态度</view></view>

            <view class="radio_title">1.学习积极性</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.learningEngagement == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'learningEngagement')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.learningEngagement == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>

            <view class="radio_title">2.是否有厌学情绪，抵触心理</view>
            <view class="radio-group">
              <view
                v-for="itemLa in tableOptions"
                :key="itemLa.value"
                class="radio-item"
                :class="{ active: addFeedbackTableCo.resistanceSigns == itemLa.value }"
                @click="addFeedbackTableClick(itemLa, 'resistanceSigns')"
              >
                <view class="circle"><view class="dot" v-if="addFeedbackTableCo.resistanceSigns == itemLa.value"></view></view>
                <text class="label">{{ itemLa.label }}</text>
              </view>
            </view>
          </view>
        </view>

        <view class="white-content marginTop30" style="height: 420rpx">
          <view class="flex-view">
            <view class="text-content">教练评语：</view>
            <view v-if="isFeedback || isEdit" class="flex-view-text" @click="cleanFeedback()">清空</view>
          </view>
          <!-- 反馈 -->
          <view style="max-height: 300upx">
            <u--textarea
              v-if="isFeedback || isEdit"
              :disabled="!isFeedback && !isEdit"
              class="feedback"
              v-model="feedback"
              :value="feedback"
              placeholder="请输入"
              count
              placeholderStyle="color:#b0b0b6"
              height="165"
              maxlength="200"
            ></u--textarea>
            <view v-else class="font26 color_black33 feedbackBoxNew marginBottom10">
              {{ feedback }}
            </view>
          </view>
        </view>
        <view class="button-sp-area marginTop40 flexs">
          <button v-if="!isFeedback && !isEdit" class="mini-btn refresh" type="default" size="mini" @click="editOption()">修改</button>
          <button v-if="isFeedback || isEdit" class="mini-btn background_green_two" type="default" size="mini" @click="addOrLook(id)">确定</button>
          <button v-else class="mini-btn background_green_two" type="default" size="mini" @click="shareJump">分享</button>
        </view>
      </view>
    </view>

    <uni-popup ref="loadingPopup" type="center" :is-mask-click="false" :lock-scroll="true" @change="onPopupChange">
      <view class="bg-ff radius-15 t-c">
        <view class="loadingpadding">
          <image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
          <view class="mt-30">加载中...</view>
          <view>请耐心等候</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script>
  import dateTime from '@/components/lanxiujuan-dyDateTime/lanxiujuan-dyDateTime.vue';
  import longDate from '@/components/long-date/long-date.vue';
  import Config from '@/utils/config.js';
  export default {
    name: 'StudyDetails',
    components: {
      dateTime,
      longDate
    },
    data() {
      return {
        options: [],
        // multiIndex: [], // 当前选中的索引
        // multiData: [], // 当前显示的三列数据
        // selectedValues: [], // 当前选中的值
        selectedVideoId: '', // 存储选中的 videoId
        gradeIndex: null, //当前选中学段索引
        gradeRange: [], //学段选择范围
        selectedGrade: {}, //当前选中学段值
        courseRange: [], //课程名称选择范围
        courseIndex: null, //课程名称索引
        selectedCourse: {}, //当前选中课程名称
        videoListShow: [],
        isMore: false,
        hasGradeName: true,
        hasSGradeName: true,
        Intention: '',
        backlist: '', // 获取反馈详情列表
        subject: '', // 课程列表详情
        timelist: {
          actualStart: '',
          actualEnd: ''
        },
        triallist: '', // 试课反馈详情
        trialclass: false, // 是否是试课反馈
        isFeedback: false, // 是否显示反馈详情确定按钮
        //反馈详情列表
        Feedback: false, // 是否显示试课反馈详情确定按钮

        feedback: '', // 弹出层文本框输入的内容

        recall: {
          // 记忆情况
          minutes: '',
          words: ''
        },
        dates: '',
        flag: false, // 防止多次请求

        isEdit: false, //是否修改

        //复习时间
        showReviewTimeDialog: false,
        reviewTimeArr: ['', '', ''],
        curChoseReviewItem: '',
        curChoseReviewIndex: -1,
        XKTCurriculumCodeArr: Config.XKTCurriculumCodeArr, //学考通相关课程大类
        value: 'yes',
        tableOptions: [
          { label: '✔', value: '1' },
          { label: '✘', value: '2' },
          { label: '无', value: '3' }
        ],
        addFeedbackTableCo: {
          preparationCompleted: null, //预习任务完成
          entryTestAccuracy: null, //进门测试题目正确率
          entryTestRate: '', //进门测试正确率(%)
          eyeFollowingScore: null, //眼神跟随教师/屏幕
          irrelevantActions: null, //无关操作次数<=2次/节课
          activeAnswers: null, //主动回答问题>=1次/节课
          interactionCount: null, //互动总次数和质量
          postClassAccuracy: null, //课后留题正确率>=70%
          mistakeKnowledgeReviewQuality: null, //错题讲解质量+知识点回顾
          learningStatus: null, //听课状态
          learningEngagement: null, //学习积极性评分
          resistanceSigns: null //厌学/抵触情绪迹象
        },
        customStyle: {
          width: '631rpx',
          height: '161rpx',
          backgroundColor: 'rgb(247, 247, 247)',
          borderRadius: '8rpx'
        },
        isLoading: false // 是否显示加载中样式
      };
    },
    computed: {
      isDisable() {
        return !this.isEdit && this.hasGradeName;
      }
    },

    onLoad(e) {
      this.dates = e.dates;
      let sendData = JSON.parse(decodeURIComponent(e.sendData));
      if (sendData != null && sendData != undefined) {
        console.log('11111111111111111111111111111111', sendData);
        this.trialclass = sendData.trialclass;
        this.isFeedback = sendData.isFeedback;
        this.triallist = sendData.triallist;
        this.subject = sendData.subject;
        this.backlist = sendData.backlist;
        console.log(this.backlist, '反馈详情列表');
        if (this.trialclass) {
          if (this.triallist != null && this.triallist != undefined) {
            this.Feedback = this.triallist.feedback === '' || this.triallist.feedback == null || this.triallist.feedback == undefined;
            this.feedback = this.triallist.feedback;
            this.Intention = this.triallist.studyIntention;
            this.timelist.actualStart = this.triallist.actualStart;
            this.timelist.actualEnd = this.triallist.actualEnd;
            this.reviewTimeArr = this.getNormalReviewTime(this.triallist.reviewDateList);
          }
        } else {
          if (this.backlist != null && this.backlist != undefined) {
            this.feedback = this.backlist.feedback;
            this.isFeedback = this.backlist.feedback === '' || this.backlist.feedback == null || this.backlist.feedback == undefined;
            this.timelist.actualStart = this.backlist.actualStart;
            this.timelist.actualEnd = this.backlist.actualEnd;
          }
          // 学习效率
          if (this.isFeedback) {
            this.backlist.studyRate = '';
          }
        }
        let curriculumCode = this.trialclass ? this.triallist.curriculumCode : this.backlist.curriculumCode;
        if (this.XKTCurriculumCodeArr.includes(curriculumCode)) {
          // 使用 async/await 确保顺序执行
          this.initAndHandleVideoEcho();
        }
      }
    },
    onReady() {},
    created() {},
    methods: {
      async initAndHandleVideoEcho() {
        await this.initMultiData(); // 等待 initMultiData 执行完成
        if (!this.trialclass && this.backlist && this.backlist.xktStatisticsDto) {
          this.hasGradeName = false;
          console.log(this.backlist.xktStatisticsDto, '正课反馈详情列表');
          this.addFeedbackTableCo = this.backlist.xktFeedbackTable ? JSON.parse(JSON.stringify(this.backlist.xktFeedbackTable)) : {};
          this.handleVideoEcho(this.backlist.xktStatisticsDto);
        }
        if (this.trialclass && this.triallist && this.triallist.xktStatisticsDto) {
          this.hasSGradeName = false;
          console.log(this.triallist.xktStatisticsDto, '试课反馈详情列表');
          this.addFeedbackTableCo = this.triallist.xktFeedbackTable ? JSON.parse(JSON.stringify(this.triallist.xktFeedbackTable)) : {};
          this.handleVideoEcho(this.triallist.xktStatisticsDto);
        }
      },

      async initMultiData() {
        // 初始化三级数据
        let studentCode = this.trialclass ? this.triallist.studentCode : this.backlist.studentCode;
        // 版本1获取全部视频改为获取已上课视频
        // let resVideo = await uni.$http.get(`/dyf/web/xktCourse/all/video?studentCode=${studentCode}`);
        let resVideo = await uni.$http.get(
          `/dyf/web/xktCourse/feedback/video?studentCode=${studentCode}&curriculumCode=${this.trialclass ? this.triallist.curriculumCode : this.backlist.curriculumCode}&studyId=${
            this.subject.id
          }`
        );
        const data = resVideo.data.data;
        this.options = JSON.parse(JSON.stringify(data)); // 保存原始数据
        console.log(data, '1视频数据');
        // 版本1单选逻辑注释
        /* // 初始化 multiData
          this.multiData = [
            data.map((item) => item.gradeName), // 学段
            data[0].courseList.map((item) => item.courseName), // 课程
            data[0].courseList[0]?.videoList.map((item) => item.videoName) // 视频
          ];
          // 初始化
          this.multiIndex = [0, 0, 0];
          console.log(this.multiData, '999999'); */
        // 版本2多选
        // 学段选择项
        this.gradeRange = this.options.map((item) => item.gradeName);
      },
      onColumnChange(e) {
        const { column, value } = e.detail;
        const newIndex = [...this.multiIndex];
        newIndex[column] = value;

        if (column === 0) {
          // 更新课程和视频
          const selectedGrade = this.options[value];
          this.multiData[1] = selectedGrade.courseList.map((item) => item.courseName);
          this.multiData[2] = selectedGrade.courseList[0].videoList.map((item) => item.videoName);
          newIndex[1] = 0; // 重置课程索引
          newIndex[2] = 0; // 重置视频索引
        } else if (column === 1) {
          // 更新视频
          const selectedGrade = this.options[newIndex[0]]; // 使用更新后的一级索引
          const selectedCourse = selectedGrade.courseList[value];
          this.multiData[2] = selectedCourse.videoList.map((item) => item.videoName);
          newIndex[2] = 0; // 重置视频索引
        }

        this.multiIndex = newIndex; // 更新 multiIndex
      },
      // 最终选择时触发
      onCascadeChange(e) {
        // this.initMultiData(); // 初始化数据
        const values = e.detail.value;
        const [gradeIndex, courseIndex, videoIndex] = values;

        const selectedGrade = this.options[gradeIndex];
        const selectedCourse = selectedGrade.courseList[courseIndex];
        const selectedVideo = selectedCourse.videoList[videoIndex];

        this.selectedValues = [selectedGrade.gradeName, selectedCourse.courseName, selectedVideo.videoName];

        this.selectedVideoId = selectedVideo.videoId; // 更新选中的视频 ID
        this.multiIndex = values; // 更新索引状态
      },
      // 选择学段
      bindPickerGradeName(e) {
        this.gradeIndex = e.detail.value;
        this.selectedGrade = this.options[this.gradeIndex];
        this.courseRange = this.selectedGrade.courseList.map((courseItem) => courseItem.courseName);
        this.selectedCourse = {};
        this.videoListShow = [];
        this.isMore = false;
      },
      // 选择课程名称
      bindPickerCourse(e) {
        console.log('1234', e, this.selectedGrade.courseList);
        this.courseIndex = e.detail.value;
        this.selectedCourse = this.selectedGrade.courseList[this.courseIndex];
        if (this.selectedCourse.videoList.length > 5) {
          this.isMore = true;
          this.videoListShow = JSON.parse(JSON.stringify(this.selectedCourse.videoList)).slice(0, 5);
        } else {
          this.videoListShow = JSON.parse(JSON.stringify(this.selectedCourse.videoList));
          this.isMore = false;
        }
        console.log(e, 'eeeeee', this.courseIndex, this.selectedCourse);
      },
      getMoreVideo() {
        this.isMore = false;
        this.videoListShow = [...this.videoListShow, ...JSON.parse(JSON.stringify(this.selectedCourse.videoList)).slice(5, this.selectedCourse.videoList.length)];
      },
      changeFeedBackType(item) {
        console.log(this.trialclass, 'item', '正课', this.isFeedback, '试课', this.Feedback);
        if ((this.trialclass && !this.Feedback && !this.isEdit) || (!this.trialclass && !this.isFeedback && !this.isEdit)) return;
        if (item.feedBackType == 1) {
          let tempShow = [];
          if (this.isMore) {
            tempShow = JSON.parse(
              JSON.stringify([...this.videoListShow, ...JSON.parse(JSON.stringify(this.selectedCourse.videoList)).slice(5, this.selectedCourse.videoList.length)])
            );
          } else {
            tempShow = JSON.parse(JSON.stringify(this.videoListShow));
          }
          let videoNum = 0;
          tempShow.forEach((videoI) => {
            if (videoI.feedBackType === 0) {
              videoNum++;
            }
          });
          if (!this.trialclass && videoNum > 9) {
            uni.showToast({
              title: '最多选择10个视频',
              icon: 'none'
            });
            return;
          } else if (this.trialclass && videoNum > 0) {
            uni.showToast({
              title: '最多选择1个视频',
              icon: 'none'
            });
            return;
          }
          item.feedBackType = 0;
        } else {
          item.feedBackType = 1;
        }
      },
      getNormalReviewTime(arr) {
        if (!arr) {
          arr = [];
        }
        if (arr.length >= 3) {
          return arr;
        }
        for (let i = 0; i < 3; i++) {
          if (!arr[i]) {
            arr.push('');
          }
        }
        return arr;
      },
      choseReview(index) {
        if (!this.Feedback && !this.isEdit) {
          return;
        }
        this.showReviewTimeDialog = true;
        this.curChoseReviewIndex = index;
        this.$refs.choseReviewDate.open();
      },
      seletReviewTime(e) {
        this.curChoseReviewItem = e.time;
      },
      cleanReview(index) {
        this.reviewTimeArr.splice(index, 1, '');
      },
      cancelChoseReview() {
        this.showReviewTimeDialog = false;
        this.$refs.choseReviewDate.close();
      },

      //清空
      cleanFeedback() {
        this.feedback = '';
      },
      //修改按钮
      async editOption() {
        this.recall.minutes = 0;
        this.recall.words = 0;

        this.isEdit = true;
        await this.initMultiData();

        // 修改--添加是否为学考通类型判断
        if (this.trialclass) {
          this.handleVideoEcho(this.triallist.xktStatisticsDto);
        } else {
          this.handleVideoEcho(this.backlist.xktStatisticsDto);
        }
      },

      // 获取试课详情
      getDetail() {
        if (this.trialclass) {
          //试课
          this.trialData(this.subject);
        } else {
          //学习反馈
          this.backData();
        }
      },

      // 刷新按钮（关闭弹框）
      async refresh() {
        let that = this;
        if (that.flag) return;
        that.flag = true;
        this.$refs.loadingPopup.open();
        try {
          let res = await uni.$http.get('/deliver/app/teacher/refreshFeedbackInfo', {
            id: this.subject.id,
            actualStart: this.timelist.actualStart,
            actualEnd: this.timelist.actualEnd,
            type: 1
          });
          this.$refs.loadingPopup.close();
          setTimeout(() => {
            that.flag = false;
          }, 60000);
          // if (res.data.code == 70001) {}
          if (res.data.success) {
            that.backlist = res.data.data;
            that.isFeedback = that.backlist.feedback === '' || that.backlist.feedback == null || that.backlist.feedback == undefined;
            that.feedback = that.backlist.feedback;
            that.timelist.actualStart = that.backlist.actualStart;
            that.timelist.actualEnd = that.backlist.actualEnd;
            this.handleVideoEcho(that.backlist.xktStatisticsDto);
          }
          that.$refs.chanelTime.setLists(that.timelist);
        } catch {
          this.$refs.loadingPopup.close();
        }
      },

      // 确定按钮（关闭弹框）
      addOrLook() {
        this.selectedVideoId = '';
        let curriculumCode = this.trialclass ? this.triallist.curriculumCode : this.backlist.curriculumCode;
        if (this.XKTCurriculumCodeArr.includes(curriculumCode)) {
          this.videoListShow.forEach((item) => {
            if (item.feedBackType != 1) {
              if (this.selectedVideoId) {
                this.selectedVideoId = this.selectedVideoId + ',' + item.videoId;
              } else {
                this.selectedVideoId = item.videoId;
              }
            }
          });
          console.log(this.videoListShow, 'xxxxxxsssssmmmm', this.selectedVideoId);
          if (!this.selectedVideoId)
            return uni.showToast({
              title: '请您选择对应的授课视频',
              icon: 'none'
            });
        }

        if (this.isEdit) {
          this.editFeedback();
          return;
        }

        if (this.subject.experience == false) {
          this.addbackData(false); // 新增反馈  // experience = true 是试课反馈
        } else {
          this.addtrialData(); // 新增试课反馈  // experience = false 是学习反馈
        }
      },

      getValidReviewArr() {
        let dateArr = [];
        for (var i = 0; i < this.reviewTimeArr.length; i++) {
          if (this.reviewTimeArr[i]) {
            dateArr.push(this.reviewTimeArr[i]);
          }
        }
        console.log(this.reviewTimeArr);
        console.log(dateArr);
        return dateArr;
      },

      async editFeedback() {
        let that = this;
        that.$refs.loadingPopup.open();
        let postUrl = {};
        postUrl = {
          feedback: that.feedback,
          studyId: that.subject.id
        };
        let curriculumCode = this.trialclass ? this.triallist.curriculumCode : this.backlist.curriculumCode;
        let url = '/deliver/app/teacher/updateFeedback';
        if (this.XKTCurriculumCodeArr.includes(curriculumCode)) {
          url = '/dyf/web/xkt/feedback/updateFeedback';
          postUrl = {
            feedback: that.feedback,
            id: that.subject.id,
            videoId: this.selectedVideoId,
            addFeedbackTableCo: this.addFeedbackTableCo,
            path: `https://document.dxznjy.com/qyWechat/#/pages/feedbackTable?data=${JSON.stringify(this.addFeedbackTableCo)}`
          };
        }
        console.log('url', url, 'postUrl', postUrl);
        try {
          let res = await uni.$http.post(url, postUrl);
          console.log(res);
          that.$refs.loadingPopup.close();
          if (res.data.success) {
            uni.showToast({
              title: '修改反馈成功',
              duration: 1000
            });
            that.isEdit = false;
            that.getDetail();
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },

      // 新增学习反馈
      async addbackData(isConfirm) {
        let that = this;
        that.$refs.loadingPopup.open();
        // 默认新增正课反馈接口路径
        let postUrl = `/deliver/app/teacher/addSimpleFeedback?actualStart=${that.timelist.actualStart}&actualEnd=${that.timelist.actualEnd}&feedBack=${encodeURI(
          encodeURI(that.feedback)
        )}&id=${that.subject.id}&type=1&studyRate=0&videoId=${that.selectedVideoId}`;
        // 学考通新增反馈单独接口
        if (that.XKTCurriculumCodeArr.includes(that.backlist.curriculumCode)) {
          postUrl = `/dyf/web/xkt/feedback/addFeedback`;
        }
        let data = {
          courseType: 1,
          actualStart: that.timelist.actualStart,
          actualEnd: that.timelist.actualEnd,
          feedback: encodeURI(encodeURI(that.feedback)),
          id: that.subject.id,
          type: 1,
          studyRate: 0,
          videoId: that.selectedVideoId,
          addFeedbackTableCo: that.addFeedbackTableCo,
          path: `https://document.dxznjy.com/qyWechat/#/pages/feedbackTable?data=${JSON.stringify(this.addFeedbackTableCo)}`
        };
        if (isConfirm) {
          // postUrl = postUrl + '&confirm=true';
          data.confirm = true;
        }
        try {
          let res = null;
          if (that.XKTCurriculumCodeArr.includes(that.backlist.curriculumCode)) {
            res = await uni.$http.post(postUrl, data);
          } else {
            res = await uni.$http.post(postUrl);
          }
          console.log(res);
          that.$refs.loadingPopup.close();
          if (res.data.code == 40047) {
            uni.showModal({
              title: '提示',
              content: res.data.message,
              success: function (res) {
                if (res.confirm) {
                  that.addbackData(true);
                } else if (res.cancel) {
                  console.log('用户点击取消');
                }
              }
            });
            return;
          }
          // 成功提示
          if (res.data.success) {
            uni.showToast({
              title: '反馈成功',
              duration: 1000
            });
            that.backlist.feedback = that.feedback;
            //显示分享按钮
            that.isFeedback = false;
            that.hasGradeName = false;
            that.getDetail();
          }
        } catch {
          that.$refs.loadingPopup.close();
        }
      },

      // 新增试课反馈
      async addtrialData() {
        let that = this;
        // 判断是否选择了视频

        that.$refs.loadingPopup.open();
        let reviewArr = this.getValidReviewArr();
        let data = {
          courseType: 2,
          feedback: that.feedback,
          memoryTime: 0,
          memoryNum: 0,
          studyId: that.subject.id,
          studyIntention: '愿意',
          actualStart: that.timelist.actualStart,
          actualEnd: that.timelist.actualEnd,
          reviewDateList: reviewArr,
          videoId: that.selectedVideoId
        };
        let postUrl = `/deliver/app/teacher/addSimpleExperienceFeedback`;
        if (that.XKTCurriculumCodeArr.includes(that.triallist.curriculumCode)) {
          postUrl = `/dyf/web/xkt/feedback/addFeedback`;
          data.id = that.subject.id;
          data.addFeedbackTableCo = that.addFeedbackTableCo;
          data.path = `https://document.dxznjy.com/qyWechat/#/pages/feedbackTable?data=${JSON.stringify(this.addFeedbackTableCo)}`;
        }
        try {
          let res = await uni.$http.post(postUrl, data);
          console.log(res);
          that.$refs.loadingPopup.close();
          if (res.data.success) {
            uni.showToast({
              title: '反馈成功',
              duration: 1000
            });
            //显示分享按钮
            that.Feedback = false;
            that.getDetail();
          }
          that.hasSGradeName = false;
        } catch {
          that.$refs.loadingPopup.close();
        }
      },

      //分享
      shareJump() {
        let sendData = {};
        sendData.trialclass = this.trialclass;
        sendData.isStudy = true;
        if (this.trialclass) {
          this.triallist.reviewTimeArr = this.reviewTimeArr;
          sendData.detailsData = this.triallist;
        } else {
          sendData.detailsData = this.backlist;
        }
        console.log(sendData, '11111111111111111');
        uni.navigateTo({
          url: '/share/xkt_share_feedback?sendData=' + encodeURIComponent(JSON.stringify(sendData))
        });
      },

      Back() {
        uni.$emit('onCalendarRefresh', this.dates);
        uni.navigateBack();
      },

      // 获取反馈详情
      async backData() {
        let that = this;
        let res = await uni.$http.get('/deliver/app/teacher/getFeedbackInfo', {
          id: this.subject.id,
          actualStart: this.timelist.actualStart,
          actualEnd: this.timelist.actualEnd,
          type: 1
        });
        if (res.data.success) {
          that.backlist = res.data.data;
          console.log('🚀 ~ backData ~ that.backlist.xktStatisticsDto?.xktGradeName:', that.backlist);
          if (that.backlist.xktStatisticsDto?.xktGradeName) {
            that.hasGradeName = false;
          } else {
            that.hasGradeName = true;
          }
          that.isFeedback = that.backlist.feedback === '' || that.backlist.feedback == null || that.backlist.feedback == undefined;
          that.feedback = that.backlist.feedback;
          that.timelist.actualStart = that.backlist.actualStart;
          that.timelist.actualEnd = that.backlist.actualEnd;
          that.addFeedbackTableCo = that.backlist.xktFeedbackTable;
          this.handleVideoEcho(that.backlist.xktStatisticsDto);
        }
      },

      // 试课反馈详情
      async trialData(item) {
        let that = this;
        let res = await uni.$http.post(`/deliver/app/teacher/getExperienceInfo/${that.subject.id}?actualStart=${that.timelist.actualStart}&actualEnd=${that.timelist.actualEnd}`);
        that.triallist = res.data.data;
        if (that.triallist.xktStatisticsDto?.xktGradeName) {
          that.hasSGradeName = false;
        } else {
          that.hasSGradeName = true;
        }
        if (res.data.code == 70001) {
          return;
        }
        if (res.data.success) {
          that.Feedback = that.triallist.feedback === '' || that.triallist.feedback == null || that.triallist.feedback == undefined;
          that.feedback = that.triallist.feedback;
          that.timelist.actualStart = that.triallist.actualStart;
          that.timelist.actualEnd = that.triallist.actualEnd;
          that.addFeedbackTableCo = that.triallist.xktFeedbackTable;
        }
      },

      // 授课视频反馈数据回显处理
      handleVideoEcho(xktStatisticsDto) {
        if (xktStatisticsDto == null || xktStatisticsDto.xktGradeName == '') {
          this.selectedGrade.gradeName = '';
          this.selectedCourse.courseName = '';
          this.videoListShow = [];
          this.isMore = false;
          this.initMultiData();
          return;
        }
        let gradeName = xktStatisticsDto.xktGradeName;
        let courseName = xktStatisticsDto.xktCourseName;

        this.gradeIndex = this.gradeRange.findIndex((item) => item === gradeName);
        console.log('gradeIndex', this.gradeIndex, this.options);
        this.selectedGrade = this.options[this.gradeIndex];
        console.log('selectedGrade', this.selectedGrade);
        this.courseRange = this.selectedGrade.courseList.map((courseItem) => courseItem.courseName);
        this.courseIndex = this.courseRange.findIndex((courseItem) => courseItem === courseName);
        if (this.isEdit) {
          this.selectedCourse = this.selectedGrade.courseList[this.courseIndex];
        } else {
          this.selectedCourse = {
            courseName: courseName,
            videoList: xktStatisticsDto.courseGradeVideos
          };
        }

        if (this.selectedCourse.videoList.length > 5) {
          this.isMore = true;
          this.videoListShow = JSON.parse(JSON.stringify(this.selectedCourse.videoList)).slice(0, 5);
        } else {
          this.isMore = false;
          this.videoListShow = JSON.parse(JSON.stringify(this.selectedCourse.videoList));
        }
      },
      addFeedbackTableClick(itemLa, prop) {
        if ((this.trialclass && !this.Feedback && !this.isEdit) || (!this.trialclass && !this.isFeedback && !this.isEdit)) return;
        console.log('itemLa.value', itemLa.value);
        this.$set(this.addFeedbackTableCo, prop, itemLa.value);
      },
      onPopupChange(e) {
        // 仅在 App 平台执行
        //#ifdef APP-PLUS
        console.log('onPopupChange', e);
        if (e.show) {
          this.isLoading = true;
        } else {
          this.isLoading = false;
        }
        //#endif
      }
    }
  };
</script>

<style lang="scss" scoped>
  .picker {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 10rpx;
    font-size: 28rpx;

    .picker-text {
      flex: 1; /* 确保文本占据剩余空间 */
      white-space: normal; /* 允许换行 */
      word-wrap: break-word; /* 长单词换行 */
      word-break: break-word; /* 支持中文换行 */
      text-overflow: ellipsis; /* 显示省略号 */
    }

    .picker-icon {
      margin-left: 10rpx;
    }
  }
  .nav-title {
    background-color: #f3f8fc;
    position: fixed;
    height: 150rpx;
    width: 100%;
  }

  .status_bar {
    text-align: center;
    font-size: 40rpx;
    color: #000;
    line-height: 220rpx;
  }

  .icon_img {
    position: absolute;
    left: 10rpx;
  }

  .container {
    background-color: #f3f8fc;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 170rpx;
    padding-bottom: 70rpx;
  }

  .page {
    width: 100%;
  }

  .details {
    display: flex;
    font-size: 25rpx;
    padding: 0 30rpx 50rpx;
    margin-top: 20rpx;

    /deep/.u-row {
      margin-bottom: 20rpx !important;
    }

    /deep/.u-demo-block__content {
      width: 98% !important;
      margin-left: 5rpx;
    }

    /deep/.u-col {
      padding: 0 !important;
    }
  }

  .left {
    font-size: 26rpx;
    align-items: center;
  }

  .right {
    position: relative;
    padding: 20rpx 20rpx 40rpx;
    border-radius: 30rpx;
    background-color: #fff;
  }

  .pic {
    position: absolute;
    left: 300rpx;
    top: 30rpx;
    height: 50rpx;
    width: 50rpx;
  }

  .rightbtn {
    float: right;
    position: absolute;
    top: 40rpx;
    right: 40rpx;
  }

  .icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 10rpx;
  }

  .right-content {
    margin-top: 10rpx;
  }

  .tickling {
    text-align: center;
    font-weight: 700;
    font-size: 30rpx;
  }

  .text-content {
    font-size: 28rpx;
    color: #000;
    margin-bottom: 30rpx;
    display: flex;
  }

  .widthAHalf {
    width: 50%;
  }

  .text_content_inner {
    width: 450rpx;
    white-space: pre-line;
    line-height: 45upx;
  }

  /deep/.u-textarea__field {
    background-color: #f7f7f7;
    border: 1rpx solid #d9d9d9;
    padding: 10rpx;
  }

  /deep/.u-textarea--disabled {
    background-color: #fff !important;
  }

  .button-sp-area {
    display: flex;
  }

  .cancel-btn {
    font-size: 24rpx;
    width: 120rpx;
    height: 55rpx;
    margin-bottom: 30rpx;
  }

  .refresh {
    margin-right: 30rpx;
    color: #2e896f !important;
    border: 1px solid #2e896f;
  }

  .refresh-view {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 30rpx;
    top: 40rpx;
  }

  .mini-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 45rpx;
    font-size: 30rpx;
    color: #fff;
    width: 690rpx;
    height: 90rpx;
    line-height: 90rpx;
  }

  .border-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 30rpx;
    font-size: 24rpx;
    color: #1a8eff;
    background-color: #fff;
    border: 1.4rpx solid #1a8eff;
    width: 160rpx;
    height: 60rpx;
  }

  // 弹框的样式设置
  /deep/.card {
    width: 680rpx;

    .white-content {
      background-color: #fff;
      border-radius: 14rpx;
      padding: 40rpx 30rpx 40rpx 30rpx;
      // overflow-y: auto;
    }

    .reality {
      display: flex;
      align-items: center;
      margin-top: 10rpx;
      line-height: 40rpx;
      font-size: 26rpx;
    }

    .begin {
      border: 1px solid #999;
      border-radius: 10rpx;
      height: 40rpx;
      line-height: 40rpx;
      width: 220rpx;
      text-align: center;
      margin-right: 6upx;
      font-size: 24rpx;
    }

    .finish {
      border: 1px solid #999;
      margin-left: 6upx;
      border-radius: 10rpx;
      height: 40rpx;
      line-height: 40rpx;
      width: 220rpx;
      text-align: center;
      font-size: 24rpx;
    }
  }

  .flexs {
    display: flex;
    align-items: center;
  }

  .borderInput {
    width: 100upx;
    border-bottom: 1upx solid #b1b1b1;
    text-align: center;
  }

  .border-bottom {
    border-bottom: 1rpx solid #efefef;
  }

  .loadingImg {
    width: 120rpx;
    height: 120rpx;
  }

  .loadingpadding {
    padding: 85rpx 250rpx;
  }

  .flex-view {
    display: flex;
    justify-content: space-between;
  }
  .flex-view-text {
    font-size: 28rpx;
    color: rgb(46, 137, 111);
  }
  .grade_picker {
    margin: 30rpx 0;
    height: 42rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 30rpx;
    color: #000000;
    line-height: 42rpx;
    padding-left: 15rpx;
  }
  .video_list {
    display: flex;
    flex-wrap: wrap;
  }
  .video_item {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    min-height: 74rpx;
    border-radius: 37rpx;
    font-size: 26rpx;
    background: #f7f7f7;
    color: #000000;
    padding: 19rpx 47rpx 18rpx 48rpx;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    line-height: 37rpx;
  }
  .video_item_check {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    min-height: 74rpx;
    border-radius: 37rpx;
    font-size: 26rpx;
    background: #1dd39a;
    color: #fff;
    padding: 19rpx 47rpx 18rpx 48rpx;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    line-height: 37rpx;
  }
  .icon_check {
    width: 30rpx;
    height: 30rpx;
    margin-left: 25rpx;
  }
  .more_btn {
    width: 100%;
    height: 42rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 30rpx;
    color: #4c4c4c;
    line-height: 42rpx;
    text-align: center;
    margin: 30rpx 0 6rpx 0;
  }
  .xkt_table {
    box-sizing: border-box;
    // width: 690rpx;
    height: 2660rpx;
    padding: 20rpx 0;
    margin-top: 26rpx;
    background: url('https://document.dxznjy.com/dxSelect/751a75dd-02e3-40a8-bc8d-4d26ce8a59cd.png');
    .table_title {
      width: 195rpx;
      height: 57rpx;
      line-height: 57rpx;
      border-radius: 0 28.5rpx 28.5rpx 0;
      color: #fff;
      background-color: #1dd39a;
      padding-left: 33rpx;
      font-size: 28rpx;
      font-weight: 700;
    }
    .study_choose {
      box-sizing: border-box;
      min-width: 120rpx;
      max-width: 176rpx;
      height: 40rpx;
      padding: 0 13rpx;
      line-height: 40rpx;
      text-align: center;
      border-radius: 8rpx;
      background-color: rgba(191, 240, 213, 0.4);
      border: 2rpx solid #5fcd91;
      font-size: 24rpx;
      color: #52be92;
      font-weight: 400;
      margin-bottom: 24rpx;
    }
    .radio_title {
      height: 50rpx;
      line-height: 50rpx;
      font-size: 28rpx;
      margin-top: 24rpx;
    }
    .radio-group {
      display: flex;
      justify-content: space-between;
      padding: 20rpx;
    }

    .radio-item {
      display: flex;
      align-items: center;
      cursor: pointer;
    }

    .circle {
      width: 34rpx;
      height: 34rpx;
      border: 2rpx solid #979797;
      border-radius: 50%;
      margin-right: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .dot {
      width: 18rpx;
      height: 18rpx;
      background-color: #1dd39a;
      border-radius: 50%;
    }

    .radio-item.active .circle {
      border-color: #1dd39a;
    }

    .label {
      font-size: 32rpx;
      color: #333;
    }
    .mark {
      /deep/.u-textarea__field {
        // width: 631rpx;
        height: 161rpx;
        background-color: rgb(247, 247, 247);
        border-radius: 8rpx;
        border: none !important;
      }
      /deep/.u-textarea__count {
        position: absolute !important;
        right: 30rpx !important;
        bottom: 26rpx !important;
        font-size: 28rpx !important;
        background-color: transparent !important;
      }
      /deep/.u-textarea .u-word-limit {
        opacity: 1 !important;
        color: #888 !important;
        z-index: 99999 !important;
      }
    }
  }
  .text_feedback {
    width: 631rpx;
    height: 161rpx;
    background-color: rgb(247, 247, 247);
    border-radius: 8rpx;
  }
</style>
<style>
  /* 禁止滑动样式 */
  .xkt_disable_scroll {
    overflow: hidden !important;
    position: fixed !important;
    bottom: 0;
    width: 100%;
    height: 100%;
  }
</style>
